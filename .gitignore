*.iml
.gradle
/local.properties
/.idea/caches
/.idea/libraries
/.idea/modules.xml
/.idea/workspace.xml
/.idea/navEditor.xml
/.idea/assetWizardSettings.xml
.DS_Store
/build
/captures
.externalNativeBuild
.cxx
local.properties

# IDE files
.idea/misc.xml
.kotlin/

# Generated files
repomix-output.xml
.cursor/rules/kotlin.mdc
.idea/gradle.xml
.idea/misc.xml
repomix-output.xml
test_health_fragment_fix.md
.idea/appInsightsSettings.xml
.idea/deploymentTargetSelector.xml
*.log
LANGUAGE_SELECTION_FIX.md
test_language_selection_fix.sh
test_remote_config.sh
thumbnail_test_logs.txt
ui_dump_fixed.xml
ui_dump.xml
window_dump.xml
docs/emoji_category_visual_feedback.md
debug_visual_inconsistency.sh
EMOJI_TOGGLE_TESTING_GUIDE.md
MERGE_SUMMARY_REPORT.md
PHASE_4_IMPLEMENTATION_SUMMARY.md
QUICK_TEST_COMMANDS.md
test_emoji_toggle.sh
TOGGLE_REGRESSION_INVESTIGATION_REPORT.md
VISUAL_INCONSISTENCY_INVESTIGATION.md
app/src/main/java/com/tqhit/battery/one/features/emoji/emoji-prd-new.md
app/src/main/java/com/tqhit/battery/one/features/emoji/emoji-testing-plan.md
app/src/main/java/com/tqhit/battery/one/features/emoji/issues.md
app/src/main/java/com/tqhit/battery/one/features/emoji/refact-emoji-accessibility-dialog.md
app/src/main/java/com/tqhit/battery/one/features/emoji/refact-emoji-accessibility-toggle-flow.md
app/src/main/java/com/tqhit/battery/one/features/emoji/refact-emoji-fix-01.md
app/src/main/java/com/tqhit/battery/one/features/emoji/refact-emoji-fix-02.md
app/src/main/java/com/tqhit/battery/one/features/emoji/refact-emoji.md
docs/animation_charging_feature.md
docs/bg_permission_dialog.md
docs/firebase-safe.md
docs/opt-animation-2.md
docs/opt-animation.md
