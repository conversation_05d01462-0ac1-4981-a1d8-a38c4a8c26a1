#!/bin/bash

# Emoji Toggle Flow Testing Script
# This script automates the build, install, and log monitoring process

echo "🚀 Starting Emoji Toggle Flow Testing..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if adb is available
if ! command -v adb &> /dev/null; then
    print_error "ADB not found. Please install Android SDK and add adb to PATH."
    exit 1
fi

# Check if device is connected
if ! adb devices | grep -q "device$"; then
    print_error "No Android device/emulator connected. Please connect a device."
    exit 1
fi

print_success "Android device detected"

# Build the app
print_status "Building the app..."
if ./gradlew assembleDebug; then
    print_success "App built successfully"
else
    print_error "Failed to build app"
    exit 1
fi

# Install the app
print_status "Installing app on device..."
if adb install -r app/build/outputs/apk/debug/app-debug.apk; then
    print_success "App installed successfully"
else
    print_error "Failed to install app"
    exit 1
fi

# Clear previous logs
print_status "Clearing previous logs..."
adb logcat -c

print_success "Setup complete!"
echo ""
echo "📱 Testing Instructions:"
echo "1. Open the app on your device"
echo "2. Navigate to emoji gallery screen"
echo "3. Tap the accessibility toggle switch to 'ON'"
echo "4. When permission dialog appears, DISMISS it (don't grant)"
echo "5. Verify the toggle immediately reverts to 'OFF'"
echo "6. Repeat test in customize screen"
echo ""
print_warning "Press Ctrl+C to stop log monitoring when done testing"
echo ""

# Start monitoring logs
print_status "Starting log monitoring..."
echo "🔍 Monitoring emoji toggle flow logs..."
echo "================================================"

# Monitor logs with emoji-specific patterns
adb logcat | grep -E "(EMOJI_TOGGLE|EMOJI_PERMISSION|EMOJI_STATE|EmojiUnifiedToggleManager)" --color=always
