#!/bin/bash

# Emoji Feature Build-Test-Deploy Automation Script
# Bundle ID: com.fc.p.tj.charginganimation.batterycharging.chargeeffect

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BUNDLE_ID="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
APK_PATH="app/build/outputs/apk/debug/app-debug.apk"
EMOJI_LOG_TAGS="EmojiCategory|EmojiItem|EmojiBattery|EmojiGallery|EmojiCustomize"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if device is connected
check_device() {
    print_status "Checking for connected Android device..."
    if ! adb devices | grep -q "device$"; then
        print_error "No Android device connected. Please connect a device and enable USB debugging."
        exit 1
    fi
    print_success "Android device detected"
}

# Function to clean and build
build_project() {
    print_status "Starting Gradle clean build..."
    
    # Clean build
    print_status "Running gradle clean..."
    ./gradlew clean
    
    # Build debug APK
    print_status "Building debug APK..."
    ./gradlew assembleDebug
    
    if [ ! -f "$APK_PATH" ]; then
        print_error "APK not found at $APK_PATH"
        exit 1
    fi
    
    print_success "Build completed successfully"
}

# Function to install APK
install_apk() {
    print_status "Installing APK to device..."
    
    # Uninstall existing app if present
    adb uninstall "$BUNDLE_ID" 2>/dev/null || true
    
    # Install new APK
    adb install "$APK_PATH"
    
    print_success "APK installed successfully"
}

# Function to launch app
launch_app() {
    print_status "Launching app..."
    adb shell am start -n "$BUNDLE_ID/.MainActivity"
    sleep 2
    print_success "App launched"
}

# Function to start logcat monitoring
start_logcat() {
    print_status "Starting logcat monitoring for emoji-related logs..."
    print_status "Log tags: $EMOJI_LOG_TAGS"
    print_status "Press Ctrl+C to stop monitoring"
    echo ""
    
    # Clear logcat buffer
    adb logcat -c
    
    # Start filtered logcat
    adb logcat | grep -E "($EMOJI_LOG_TAGS)"
}

# Function to run unit tests
run_tests() {
    print_status "Running unit tests..."
    ./gradlew testDebugUnitTest
    print_success "Unit tests completed"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --build-only     Only build the project (no install/deploy)"
    echo "  --test-only      Only run unit tests"
    echo "  --install-only   Only install APK (assumes already built)"
    echo "  --logcat-only    Only start logcat monitoring"
    echo "  --full           Full cycle: clean, build, test, install, launch, monitor (default)"
    echo "  --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Full build-test-deploy cycle"
    echo "  $0 --build-only       # Just build the APK"
    echo "  $0 --test-only        # Just run tests"
    echo "  $0 --logcat-only      # Just monitor logs"
}

# Main execution
main() {
    print_status "=== Emoji Feature Build-Test-Deploy Script ==="
    print_status "Bundle ID: $BUNDLE_ID"
    echo ""
    
    case "${1:-full}" in
        --build-only)
            build_project
            ;;
        --test-only)
            run_tests
            ;;
        --install-only)
            check_device
            install_apk
            launch_app
            ;;
        --logcat-only)
            check_device
            start_logcat
            ;;
        --full|full)
            check_device
            build_project
            run_tests
            install_apk
            launch_app
            start_logcat
            ;;
        --help)
            show_usage
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
