#!/bin/bash

# Debug script for Visual Inconsistency Investigation
# This script helps filter and analyze logs related to the battery charging animation visual inconsistency

echo "=========================================="
echo "Visual Inconsistency Debug Log Filter"
echo "=========================================="
echo ""

# Function to show usage
show_usage() {
    echo "Usage: $0 [option]"
    echo ""
    echo "Options:"
    echo "  all          - Show all VISUAL_INCONSISTENCY_DEBUG logs"
    echo "  preview      - Show only preview-related logs"
    echo "  overlay      - Show only overlay-related logs"
    echo "  apply        - Show only apply customization logs"
    echo "  urls         - Show only image URL logs"
    echo "  live         - Start live monitoring (Ctrl+C to stop)"
    echo "  help         - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 all       # Show all visual inconsistency debug logs"
    echo "  $0 live      # Start live monitoring of logs"
    echo "  $0 urls      # Show only image URL related logs"
}

# Function to filter all visual inconsistency logs
filter_all() {
    echo "Filtering all VISUAL_INCONSISTENCY_DEBUG logs..."
    echo "Press Ctrl+C to stop"
    echo ""
    adb logcat | grep -E "VISUAL_INCONSISTENCY_DEBUG"
}

# Function to filter preview logs
filter_preview() {
    echo "Filtering PREVIEW related logs..."
    echo "Press Ctrl+C to stop"
    echo ""
    adb logcat | grep -E "VISUAL_INCONSISTENCY_DEBUG.*PREVIEW"
}

# Function to filter overlay logs
filter_overlay() {
    echo "Filtering OVERLAY related logs..."
    echo "Press Ctrl+C to stop"
    echo ""
    adb logcat | grep -E "VISUAL_INCONSISTENCY_DEBUG.*OVERLAY"
}

# Function to filter apply customization logs
filter_apply() {
    echo "Filtering APPLY CUSTOMIZATION related logs..."
    echo "Press Ctrl+C to stop"
    echo ""
    adb logcat | grep -E "VISUAL_INCONSISTENCY_DEBUG.*(APPLY|COMPOSITE|SUMMARY)"
}

# Function to filter URL logs
filter_urls() {
    echo "Filtering IMAGE URL related logs..."
    echo "Press Ctrl+C to stop"
    echo ""
    adb logcat | grep -E "VISUAL_INCONSISTENCY_DEBUG.*(URL|Image.*URL)"
}

# Function to start live monitoring
live_monitor() {
    echo "Starting live monitoring of visual inconsistency logs..."
    echo "1. Open the app and navigate to emoji battery customization"
    echo "2. Select different battery and emoji combinations"
    echo "3. Press Apply Customization button"
    echo "4. Check the overlay display"
    echo "5. Press Ctrl+C to stop monitoring"
    echo ""
    echo "Monitoring logs..."
    echo "=================="
    adb logcat -c  # Clear existing logs
    adb logcat | grep -E "(VISUAL_INCONSISTENCY_DEBUG|EmojiCustomizeActivity|CustomizeViewModel|EmojiBatteryView)" --color=always
}

# Main script logic
case "${1:-help}" in
    "all")
        filter_all
        ;;
    "preview")
        filter_preview
        ;;
    "overlay")
        filter_overlay
        ;;
    "apply")
        filter_apply
        ;;
    "urls")
        filter_urls
        ;;
    "live")
        live_monitor
        ;;
    "help"|*)
        show_usage
        ;;
esac
