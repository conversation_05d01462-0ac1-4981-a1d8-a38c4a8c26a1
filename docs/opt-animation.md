I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end. Make sure you fix all the linting, compilation or validation issues after successful implementation of the plan.

### Observations

I analyzed the current codebase and found that it has a custom video preloading system but lacks ExoPlayer cache integration. The app downloads videos twice - once for preview and again when applying animations. There are established permission patterns but overlay permission is only requested after user interaction. The loading dialog shows unconditionally, causing flashing for cached content.

### Approach

The implementation will add ExoPlayer's SimpleCache for unified caching, implement proactive overlay permission requests using existing patterns, and make loading indicators conditional based on cache status. This leverages the existing preloading infrastructure while adding ExoPlayer cache as the primary mechanism, with the current system as fallback.

### Reasoning

I explored the codebase structure and found the current video handling in `AnimationActivity.kt`, `VideoUtils.kt`, and related components. I analyzed existing permission patterns in `OverlayPermissionUtils.kt` and other activities. I examined the `ProgressLoadingDialog.kt` implementation and identified integration points for the optimizations.

## Implementation Progress

✅ **ExoPlayer Cache Dependencies Added**
- Added `androidx.media3:media3-datasource:1.2.1` and `androidx.media3:media3-database:1.2.1` dependencies to `app/build.gradle.kts`

✅ **ExoPlayerCacheModule Created**  
- Created new Hilt DI module with `SimpleCache`, `CacheDataSource.Factory`, and `DatabaseProvider` singletons
- Configured with 150MB cache size using `LeastRecentlyUsedCacheEvictor`
- Cache stored in `context.cacheDir/exoplayer_cache`

✅ **ExoPlayerCacheUtils Created**
- Utility class for cache operations with `isFullyCached()`, `copyFromCacheToFile()`, `getCacheSize()`, `clearCache()` methods
- Proper error handling and fallback to existing VideoUtils when cache incomplete
- Note: Direct cache-to-file extraction simplified due to ExoPlayer API limitations

✅ **VideoUtils Enhanced** 
- Integrated ExoPlayer cache support with priority logic: 1) ExoPlayer cache, 2) preloaded files, 3) network download
- Updated `downloadAndSaveVideoEnhanced()` and `isVideoPreloaded()` methods
- Maintains backward compatibility and existing error handling patterns

✅ **AnimationActivity Updated**
- **ExoPlayer Cache Integration**: Injected `CacheDataSource.Factory` and modified `setupPlayer()` to use cached data source
- **Proactive Overlay Permission**: Added early permission check in `setupData()` with banner UI management
- **Smart Loading Indicators**: Conditional progress dialog based on cache status with 200ms debounce
- Added overlay permission banner UI component above apply button with proper visibility control

✅ **Activity Layout Updated**
- Added overlay permission banner to `activity_animation.xml` with proper constraints and styling
- Banner includes icon, message text, and action button following app's design patterns

✅ **String Resources Added**
- Added banner-specific strings: `overlay_permission_banner_title`, `overlay_permission_banner_message`, `overlay_permission_banner_action`
- Added enhanced loading strings: `video_cached`, `loading_from_cache`

✅ **PostAnimationCompleteActivity Updated**
- Applied ExoPlayer cache integration with `CacheDataSource.Factory` injection
- Added smart loading indicators for instant cache-based playback
- Maintains existing functionality for post-animation completion flow

✅ **ChargingOverlayActivity Updated**  
- Integrated ExoPlayer cache with local file priority for overlay playback
- Cache used as fallback while maintaining reliable local file loading
- Performance optimization ensures overlay plays instantly from previously cached content

✅ **Compilation Successful**
- All components compiled successfully without errors
- Build completed with only deprecation warnings (pre-existing)
- Project ready for testing

## Next Steps for Testing

1. **Test Cache Integration**: Verify videos are cached during playback and reused on subsequent views
2. **Test Proactive Permissions**: Confirm overlay permission banner appears when needed and hides after grant
3. **Test Smart Loading**: Check that loading dialog only appears for uncached content with proper debounce
4. **Test Performance**: Validate faster video loading and instant overlay playback after initial cache

## Mermaid Diagram

sequenceDiagram
    participant User
    participant AnimationActivity
    participant ExoPlayerCache
    participant OverlayPermission
    participant VideoUtils
    participant ProgressDialog

    User->>AnimationActivity: Opens animation preview
    AnimationActivity->>OverlayPermission: Check permission early (proactive)
    OverlayPermission-->>AnimationActivity: Show banner if needed
    
    AnimationActivity->>ExoPlayerCache: Check if video cached
    alt Video is cached
        ExoPlayerCache-->>AnimationActivity: Video available
        Note over ProgressDialog: Skip loading dialog
        AnimationActivity->>ExoPlayerCache: Play from cache
    else Video not cached
        AnimationActivity->>ProgressDialog: Show loading dialog
        AnimationActivity->>ExoPlayerCache: Stream & cache video
        ExoPlayerCache-->>ProgressDialog: Update progress
        ExoPlayerCache-->>AnimationActivity: Video ready
        ProgressDialog->>ProgressDialog: Dismiss
    end
    
    User->>AnimationActivity: Clicks Apply
    alt Permission granted
        AnimationActivity->>VideoUtils: Copy from cache to file
        VideoUtils->>ExoPlayerCache: Extract cached video
        ExoPlayerCache-->>VideoUtils: Instant copy (no download)
        VideoUtils-->>AnimationActivity: File ready instantly
        Note over User: "Saving video..." appears briefly
    else Permission not granted
        Note over User: Already handled by banner
    end

## Proposed File Changes

sequenceDiagram
    participant User
    participant AnimationActivity
    participant ExoPlayerCache
    participant OverlayPermission
    participant VideoUtils
    participant ProgressDialog

    User->>AnimationActivity: Opens animation preview
    AnimationActivity->>OverlayPermission: Check permission early (proactive)
    OverlayPermission-->>AnimationActivity: Show banner if needed
    
    AnimationActivity->>ExoPlayerCache: Check if video cached
    alt Video is cached
        ExoPlayerCache-->>AnimationActivity: Video available
        Note over ProgressDialog: Skip loading dialog
        AnimationActivity->>ExoPlayerCache: Play from cache
    else Video not cached
        AnimationActivity->>ProgressDialog: Show loading dialog
        AnimationActivity->>ExoPlayerCache: Stream & cache video
        ExoPlayerCache-->>ProgressDialog: Update progress
        ExoPlayerCache-->>AnimationActivity: Video ready
        ProgressDialog->>ProgressDialog: Dismiss
    end
    
    User->>AnimationActivity: Clicks Apply
    alt Permission granted
        AnimationActivity->>VideoUtils: Copy from cache to file
        VideoUtils->>ExoPlayerCache: Extract cached video
        ExoPlayerCache-->>VideoUtils: Instant copy (no download)
        VideoUtils-->>AnimationActivity: File ready instantly
        Note over User: "Saving video..." appears briefly
    else Permission not granted
        Note over User: Already handled by banner
    end

## Proposed File Changes

### app/build.gradle.kts(MODIFY)

Add ExoPlayer cache dependencies to enable caching functionality. Add the following dependencies in the dependencies block:

```kotlin
implementation("androidx.media3:media3-datasource:1.2.1")
implementation("androidx.media3:media3-database:1.2.1")
```

These dependencies provide the `SimpleCache`, `CacheDataSource`, and database support needed for ExoPlayer caching implementation.

### app/src/main/java/com/tqhit/battery/one/di/ExoPlayerCacheModule.kt(NEW)

References: 

- app/src/main/java/com/tqhit/battery/one/di/ThumbnailPreloadingModule.kt

Create a new Hilt module to provide ExoPlayer cache components as singletons. This module will provide:

1. **SimpleCache**: Configure with 150MB cache size using `LeastRecentlyUsedCacheEvictor` and store in `context.cacheDir/exoplayer_cache`
2. **CacheDataSource.Factory**: Combine cache with network data source for seamless caching
3. **DatabaseProvider**: Required for cache metadata storage

The module should use `@Singleton` scope to ensure cache consistency across the app. Include proper error handling and logging using `BatteryLogger` similar to other DI modules in the project.

### app/src/main/java/com/tqhit/battery/one/utils/ExoPlayerCacheUtils.kt(NEW)

References: 

- app/src/main/java/com/tqhit/battery/one/utils/VideoUtils.kt(MODIFY)

Create utility class for ExoPlayer cache operations. This class will provide:

1. **isFullyCached(url)**: Check if a video URL is completely cached using `CacheUtil.getCached()`
2. **copyFromCacheToFile(url, destFile)**: Extract cached video to internal storage for overlay service
3. **getCacheSize()**: Get current cache usage for monitoring
4. **clearCache()**: Clear cache if needed

Implement proper error handling and fallback to the existing `VideoUtils.downloadAndSaveVideoEnhanced()` when cache is incomplete. Use `@Singleton` and inject `SimpleCache` via Hilt. Follow the logging patterns established in `VideoUtils.kt`.

### app/src/main/java/com/tqhit/battery/one/utils/VideoUtils.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/repository/AnimationPreloadingRepository.kt

Enhance the `VideoUtils` class to integrate with ExoPlayer cache:

1. **Inject ExoPlayerCacheUtils**: Add dependency injection for the new cache utility
2. **Update downloadAndSaveVideoEnhanced()**: Modify the method to first check ExoPlayer cache using `ExoPlayerCacheUtils.copyFromCacheToFile()`, then fall back to preloaded files, and finally network download
3. **Update isVideoPreloaded()**: Extend to also check ExoPlayer cache status using `ExoPlayerCacheUtils.isFullyCached()`
4. **Add cache priority logic**: Implement precedence order: 1) ExoPlayer cache, 2) preloaded files, 3) network download

Maintain backward compatibility and existing error handling patterns. Update logging to distinguish between cache sources (ExoPlayer cache vs preloaded files).

### app/src/main/java/com/tqhit/battery/one/activity/animation/AnimationActivity.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/utils/OverlayPermissionUtils.kt
- app/src/main/java/com/tqhit/battery/one/activity/starting/StartingActivity.kt

Implement all three optimizations in the AnimationActivity:

**1. ExoPlayer Cache Integration:**
- Inject `CacheDataSource.Factory` via Hilt
- Modify `setupPlayer()` to use cached data source factory when building ExoPlayer
- Update `proceedAfterOverlayPermission()` to use enhanced `VideoUtils.downloadAndSaveVideoEnhanced()` which now leverages cache

**2. Proactive Overlay Permission:**
- Add early permission check in `setupData()` after line 105
- Use `OverlayPermissionUtils.requestOverlayPermissionIfNeeded()` while video is loading
- Add permission banner UI state management
- Update `updateButton()` method to handle permission state alongside trial state

**3. Smart Loading Indicators:**
- Modify `setupPlayer()` to check cache status before showing `ProgressLoadingDialog`
- Use `VideoUtils.isVideoPreloaded()` (now enhanced with cache checking) to conditionally show dialog
- Add 200ms debounce to prevent flashing for near-instant loads
- Only show progress monitoring for actual network buffering

Maintain existing ad loading, time/date updates, and battery monitoring functionality. Follow established error handling patterns from `OverlayPermissionUtils.kt`.

### app/src/main/res/layout/activity_animation.xml(MODIFY)

References: 

- app/src/main/res/layout/activity_starting.xml

Add overlay permission banner UI component to the activity layout:

1. **Add permission banner**: Insert a banner view (similar to notification banners in other activities) above the apply button section
2. **Banner content**: Include icon, message text "To apply animations, enable overlay permission", and action button
3. **Visibility control**: Set initial visibility to `gone`, will be controlled programmatically
4. **Styling consistency**: Use existing color schemes and dimensions following the app's design patterns
5. **Constraints**: Position banner to not interfere with video player or existing UI elements

Reference the banner implementations in other activities and maintain consistent spacing and styling with the existing apply button area.

### app/src/main/java/com/tqhit/battery/one/activity/animation/onCompleted/PostAnimationCompleteActivity.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/activity/animation/AnimationActivity.kt(MODIFY)

Apply ExoPlayer cache integration and smart loading indicators to maintain consistency:

1. **ExoPlayer Cache**: Inject `CacheDataSource.Factory` and modify player setup to use cached data source
2. **Smart Loading**: Apply the same conditional loading dialog logic as `AnimationActivity` - check if video is cached before showing progress dialog
3. **Cache-aware playback**: Since this activity plays the same video that was just applied, it should load instantly from cache

Maintain existing functionality for post-animation completion flow, ad management, and navigation. Use the same debounce timing (200ms) and cache checking patterns established in `AnimationActivity.kt`.

### app/src/main/java/com/tqhit/battery/one/activity/overlay/ChargingOverlayActivity.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/activity/animation/AnimationActivity.kt(MODIFY)

Apply ExoPlayer cache integration for overlay playback:

1. **ExoPlayer Cache**: Inject `CacheDataSource.Factory` and modify player setup to use cached data source
2. **Local file priority**: Since overlay plays from internal storage file, maintain existing local file loading but add cache as fallback
3. **Performance optimization**: The overlay should play instantly since the file was just copied from cache

Maintain existing overlay functionality, window management, and video looping behavior. This ensures the overlay experience benefits from the unified caching system while preserving the reliable local file playback.

### app/src/main/res/values/strings.xml(MODIFY)

References: 

- app/src/main/res/values-ar/strings.xml

Add new string resources for the overlay permission banner and enhanced loading states:

1. **Permission banner strings**:
   - `overlay_permission_banner_message`: "To apply animations, enable overlay permission"
   - `overlay_permission_banner_action`: "Enable"
   - `overlay_permission_banner_title`: "Permission Required"

2. **Enhanced loading strings**:
   - `video_cached`: "Video ready from cache"
   - `loading_from_cache`: "Loading from cache..."

Maintain consistency with existing string naming conventions and ensure these strings are added to all localization files in the `values-*` directories to support the app's multi-language functionality.