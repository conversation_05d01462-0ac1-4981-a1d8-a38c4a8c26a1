I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end. Make sure you fix all the linting, compilation or validation issues after successful implementation of the plan.

### Observations

The crash occurs because Firebase is initialized asynchronously while Hilt dependency injection happens synchronously during `super.onCreate()`. The external adlib SDK's `FirebaseModule` tries to provide `FirebaseRemoteConfig` during Hilt initialization, but Firebase isn't ready yet. The key challenge is that `FirebaseRemoteConfigHelper` is a concrete class from the adlib SDK, not an interface, which limits our ability to create simple wrapper implementations.

### Approach

I'll implement a solution that uses Hilt's `Provider<T>` pattern to delay the instantiation of `FirebaseRemoteConfigHelper` until Firebase is actually ready. This involves creating a `LazyFirebaseRemoteConfigProvider` that checks Firebase readiness before creating the helper, and a `FirebaseReadinessManager` to coordinate the async initialization. The solution uses <PERSON><PERSON>'s capability to override module bindings to replace the adlib SDK's direct FirebaseRemoteConfigHelper provision with our lazy provider.

### Reasoning

I analyzed the crash stack trace and examined the codebase to understand Firebase usage patterns. I discovered that `FirebaseRemoteConfigHelper` is extensively used throughout the app and is a concrete class from the external adlib SDK. Since we can't modify the external class or create interface-based wrappers, I need to use Hilt's Provider pattern to delay instantiation until Firebase is ready.

## Mermaid Diagram

sequenceDiagram
    participant App as BatteryApplication
    participant Hilt as Hilt DI
    participant Provider as LazyFirebaseRemoteConfigProvider
    participant ReadinessManager as FirebaseReadinessManager
    participant Firebase as FirebaseApp
    participant AdLib as AdLib SDK
    
    App->>App: onCreate() starts
    App->>Hilt: super.onCreate() - Hilt initialization
    Hilt->>AdLib: Inject Firebase dependencies
    AdLib->>Provider: Request FirebaseRemoteConfigHelper (via override)
    Provider->>ReadinessManager: waitForFirebaseReady(timeout=5s)
    
    par Async Firebase Initialization
        App->>Firebase: initializeAsyncComponents() - async Firebase init
        Firebase-->>App: Firebase initialized
        App->>ReadinessManager: markFirebaseReady()
        ReadinessManager-->>Provider: Firebase ready signal
    end
    
    Provider->>Provider: Create actual FirebaseRemoteConfigHelper
    Provider-->>AdLib: Return FirebaseRemoteConfigHelper instance
    AdLib-->>Hilt: Firebase dependencies provided
    Hilt-->>App: Hilt initialization complete
    
    alt Firebase timeout
        ReadinessManager-->>Provider: Timeout after 5s
        Provider->>Provider: Create DefaultRemoteConfigHelper
        Provider-->>AdLib: Return fallback helper with defaults
    end

## Proposed File Changes

### app/src/main/java/com/tqhit/battery/one/firebase/FirebaseReadinessManager.kt(MODIFY)

Create a `FirebaseReadinessManager` singleton class that tracks Firebase initialization state and coordinates async Firebase initialization. This class includes:

1. **Firebase readiness tracking**: Uses `AtomicBoolean` for thread-safe state management
2. **Blocking wait mechanism**: Provides `waitForFirebaseReady()` method that blocks until Firebase is initialized or times out
3. **Timeout handling**: Implements a reasonable timeout (5 seconds) to prevent indefinite blocking
4. **State checking**: Offers methods to check if Firebase is initialized
5. **Initialization callback**: Provides a method to mark Firebase as ready after initialization
6. **Thread safety**: Ensures safe access from multiple threads during app startup
7. **Coroutine support**: Uses `CompletableDeferred` for async coordination

This manager acts as a central coordinator for Firebase-dependent components, allowing them to wait for Firebase initialization without crashing.

### app/src/main/java/com/tqhit/battery/one/firebase/LazyFirebaseRemoteConfigProvider.kt(NEW)

References: 

- app/src/main/java/com/tqhit/battery/one/firebase/FirebaseReadinessManager.kt(MODIFY)

Create a `LazyFirebaseRemoteConfigProvider` class that implements `Provider<FirebaseRemoteConfigHelper>` to delay instantiation until Firebase is ready. This provider includes:

1. **Lazy instantiation**: Only creates `FirebaseRemoteConfigHelper` when `get()` is called and Firebase is ready
2. **Firebase readiness check**: Uses `FirebaseReadinessManager` to ensure Firebase is initialized before creating the helper
3. **Timeout handling**: Waits for Firebase with a reasonable timeout to prevent indefinite blocking
4. **Fallback mechanism**: If Firebase isn't ready within timeout, creates a mock helper that returns default values
5. **Thread safety**: Ensures safe creation of the helper instance
6. **Singleton pattern**: Caches the created instance to avoid multiple instantiations
7. **Error handling**: Gracefully handles Firebase initialization failures

This provider ensures that `FirebaseRemoteConfigHelper` is only created when Firebase is actually ready, preventing the crash during Hilt injection.

### app/src/main/java/com/tqhit/battery/one/firebase/DefaultRemoteConfigHelper.kt(NEW)

References: 

- app/src/main/res/xml/remote_config_defaults.xml

Create a `DefaultRemoteConfigHelper` class that extends or mimics `FirebaseRemoteConfigHelper` to provide default values when Firebase isn't ready. This helper includes:

1. **Default value provision**: Returns hardcoded default values that match those in `remote_config_defaults.xml`
2. **Method compatibility**: Provides the same methods as `FirebaseRemoteConfigHelper` (`getString()`, `getBoolean()`, `getLong()`)
3. **No Firebase dependency**: Works without requiring Firebase to be initialized
4. **Fallback behavior**: Ensures the app can function normally with sensible defaults
5. **Type safety**: Maintains the same return types as the original helper
6. **Configuration mapping**: Maps all the configuration keys used in the app to appropriate default values

This helper serves as a fallback when Firebase initialization times out or fails, ensuring the app continues to work with default configurations.

### app/src/main/java/com/tqhit/battery/one/di/FirebaseSafetyModule.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/firebase/LazyFirebaseRemoteConfigProvider.kt(NEW)
- app/src/main/java/com/tqhit/battery/one/firebase/FirebaseReadinessManager.kt(MODIFY)

Create a Hilt module `FirebaseSafetyModule` that overrides the adlib SDK's Firebase dependencies with safe lazy providers. This module includes:

1. **FirebaseRemoteConfigHelper override**: Uses `@Provides` with higher priority to override the adlib SDK's direct provision
2. **Lazy provider binding**: Binds `LazyFirebaseRemoteConfigProvider` as the provider for `FirebaseRemoteConfigHelper`
3. **FirebaseReadinessManager provision**: Provides the singleton `FirebaseReadinessManager` instance
4. **Module priority**: Uses appropriate Hilt annotations to ensure this module takes precedence over the adlib SDK's module
5. **Singleton scope**: Ensures single instances of safety components
6. **Context provision**: Provides necessary context for default value loading

This module ensures that when Hilt tries to inject `FirebaseRemoteConfigHelper`, it gets our lazy provider instead of the direct instantiation that causes the crash.

### app/src/main/java/com/tqhit/battery/one/BatteryApplication.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/firebase/FirebaseReadinessManager.kt(MODIFY)

Modify the `BatteryApplication` class to integrate with the Firebase safety system while keeping async initialization. The changes include:

1. **Inject FirebaseReadinessManager**: Add injection of the `FirebaseReadinessManager` to coordinate Firebase state
2. **Update async Firebase initialization**: Modify `initializeAsyncComponents()` to notify the readiness manager when Firebase is initialized
3. **Add Firebase readiness callback**: Call `firebaseReadinessManager.markFirebaseReady()` after successful Firebase initialization in the async block
4. **Maintain async nature**: Keep Firebase initialization in the background thread to preserve startup performance
5. **Add error handling**: Ensure Firebase readiness is handled even if initialization fails
6. **Import new dependencies**: Add imports for the new Firebase safety classes
7. **Add timeout logging**: Log if Firebase initialization takes longer than expected

This ensures that the Firebase safety system is properly integrated while maintaining the async initialization approach, and components waiting for Firebase will be notified when it's ready.