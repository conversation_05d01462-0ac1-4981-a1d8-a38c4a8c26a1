I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end. Make sure you fix all the linting, compilation or validation issues after successful implementation of the plan.

### Observations

Based on my exploration of the codebase, I found that the BackgroundPermissionDialog is currently shown during MainActivity startup (onCreate and onResume) with rate limiting. The animation charging flow goes: AnimationGridFragment → AnimationActivity → PostAnimationCompleteActivity after successful overlay permission and animation application. The user wants to keep the existing logic for future use but comment out the dialog appearance in MainActivity startup, and add new logic to show the dialog in PostAnimationCompleteActivity after successful overlay animation application.

### Approach

The implementation will involve: 1) Comment out the background permission dialog calls in MainActivity startup flow while preserving the existing methods for future use, 2) Add new logic to PostAnimationCompleteActivity to show the dialog after successful overlay animation application, and 3) Leverage existing methods in BackgroundPermissionManager, potentially using the `shouldShowBackgroundPermissionDialogIgnoreCooldown()` method since we want to show the dialog immediately after animation application regardless of previous dismissals. This approach maintains all existing code while only changing the trigger timing.

### Reasoning

I explored the codebase structure and identified the key files involved in the animation charging flow and background permission dialog management. I examined AnimationActivity.kt, PostAnimationCompleteActivity.kt, MainActivity.kt, BackgroundPermissionDialog.kt, and BackgroundPermissionManager.kt to understand the current implementation and integration points. I found that BackgroundPermissionManager already has a `shouldShowBackgroundPermissionDialogIgnoreCooldown()` method that could be used for the post-animation context.

## Mermaid Diagram

sequenceDiagram
    participant User
    participant AnimationActivity
    participant PostAnimationCompleteActivity
    participant BackgroundPermissionManager
    participant BackgroundPermissionDialog
    
    User->>AnimationActivity: Tap "Apply" button
    AnimationActivity->>AnimationActivity: Show rewarded ad
    AnimationActivity->>AnimationActivity: Check overlay permission
    AnimationActivity->>AnimationActivity: Apply animation (proceedAfterOverlayPermission)
    AnimationActivity->>PostAnimationCompleteActivity: Start activity after success
    AnimationActivity->>AnimationActivity: finish()
    
    PostAnimationCompleteActivity->>PostAnimationCompleteActivity: onCreate() & setupUI()
    PostAnimationCompleteActivity->>PostAnimationCompleteActivity: setupPlayer() & setupAntiThiefInfo()
    PostAnimationCompleteActivity->>BackgroundPermissionManager: shouldShowBackgroundPermissionDialogIgnoreCooldown()
    BackgroundPermissionManager->>BackgroundPermissionManager: Check if permission already granted (no cooldown)
    BackgroundPermissionManager-->>PostAnimationCompleteActivity: Return true/false
    
    alt Permission not granted
        PostAnimationCompleteActivity->>BackgroundPermissionDialog: Create and show dialog
        BackgroundPermissionDialog->>User: Display permission request
        User->>BackgroundPermissionDialog: Grant or deny permission
        BackgroundPermissionDialog->>PostAnimationCompleteActivity: Callback with result
    else Permission already granted
        PostAnimationCompleteActivity->>PostAnimationCompleteActivity: Skip dialog, continue normal flow
    end

## Proposed File Changes

### app/src/main/java/com/tqhit/battery/one/activity/main/MainActivity.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/utils/BackgroundPermissionManager.kt

Comment out the background permission dialog calls in MainActivity startup flow while preserving all existing methods for future use. In the `onCreate()` method around line 168, comment out the call to `checkAndShowBackgroundPermissionDialog()` and add a comment explaining that the dialog has been moved to post-animation flow. Similarly, in the `onResume()` method around line 563, comment out the call to `checkAndShowBackgroundPermissionDialog()` with the same explanatory comment. Keep all existing methods (`checkAndShowBackgroundPermissionDialog()`, `showBackgroundPermissionDialog()`) intact and functional for potential future use. Keep the `backgroundPermissionDialog` field and cleanup logic in `onDestroy()` unchanged since the methods might be re-enabled in the future. Add comments indicating that these methods are preserved for future use but currently disabled in favor of post-animation dialog flow.

### app/src/main/java/com/tqhit/battery/one/activity/animation/onCompleted/PostAnimationCompleteActivity.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/dialog/permission/BackgroundPermissionDialog.kt
- app/src/main/java/com/tqhit/battery/one/utils/BackgroundPermissionManager.kt

Add background permission dialog functionality to show right after successful overlay animation application. Import the required classes: `BackgroundPermissionDialog` and `BackgroundPermissionManager`. Add a private field `backgroundPermissionDialog: BackgroundPermissionDialog?` to track the dialog instance. Create a new private method `checkAndShowBackgroundPermissionDialogAfterAnimation()` that uses the existing `BackgroundPermissionManager.shouldShowBackgroundPermissionDialogIgnoreCooldown()` method to determine if the dialog should be shown (this ignores cooldown since we want immediate display after animation). Include proper lifecycle checks (isFinishing, isDestroyed) and ensure no dialog is already showing. Create a `showBackgroundPermissionDialogAfterAnimation()` method similar to MainActivity's implementation but adapted for PostAnimationCompleteActivity context with appropriate callbacks. Call `checkAndShowBackgroundPermissionDialogAfterAnimation()` in the `setupUI()` method after the existing `setupPlayer()` and `setupAntiThiefInfo()` calls, ensuring the dialog appears after the UI is properly initialized. Add proper cleanup in `onDestroy()` to dismiss the dialog and set the reference to null. Include comprehensive logging to track when the dialog is shown in the post-animation context, using a distinct tag like 'PostAnimationPermission' to differentiate from MainActivity logs.