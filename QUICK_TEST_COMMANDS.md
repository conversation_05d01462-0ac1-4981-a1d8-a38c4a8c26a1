# Quick Test Commands for Emoji Toggle Flow

## 🚀 Essential Commands

### 1. Build & Install
```bash
./gradlew assembleDebug && adb install -r app/build/outputs/apk/debug/app-debug.apk
```

### 2. Monitor Logs (Primary)
```bash
adb logcat | grep -E "(EMOJI_TOGGLE|EMOJI_PERMISSION|EMOJI_STATE|EmojiUnifiedToggleManager)"
```

### 3. Monitor Critical State Sync
```bash
adb logcat | grep -E "(EMOJI_STATE_SYNC|EMOJI_TOGGLE_FLOW|EMOJI_PERMISSION_DIALOG)"
```

### 4. Clear Logs
```bash
adb logcat -c
```

## 🎯 Critical Test Flow

1. **Start monitoring**: Run log command above
2. **Open app**: Navigate to emoji gallery
3. **Tap toggle ON**: Should show permission dialog
4. **Dismiss dialog**: Tap outside or back button
5. **Verify**: Toggle immediately shows OFF

## 🔍 Key Log Patterns to Look For

### ✅ Success Pattern:
```
👆 USER_TAP: User tapped toggle to true
🔐 PERMISSION_DIALOG_STARTING
🔄 StateFlow EMITTING: false
📡 RECEIVED: StateFlow emitted false
⚡ MISMATCH: UI state (true) != Manager state (false) - FIXING
✅ UI_UPDATED: true → false
🎯 SUCCESS: UI toggle correctly shows false
```

### ❌ Failure Pattern:
```
👆 USER_TAP: User tapped toggle to true
🔐 PERMISSION_DIALOG_STARTING
(Missing StateFlow emission logs)
(UI remains stuck on true)
```

## 📱 Test Scenarios

1. **Gallery Screen**: Tap toggle → dismiss dialog → verify OFF
2. **Customize Screen**: Tap toggle → dismiss dialog → verify OFF  
3. **Cross-Screen**: Check toggle state consistency between screens
4. **Permission Grant**: Grant permission → verify toggle stays ON

## 🚨 Quick Troubleshooting

- **No logs**: Try `adb logcat | grep -i emoji`
- **No dialog**: Check if permission already granted in Settings
- **Toggle stuck**: Look for StateFlow emission logs
- **App crash**: Check for error logs with `adb logcat | grep -E "(ERROR|FATAL)"`

## 🎉 Success Criteria

✅ Toggle immediately reverts to OFF when dialog dismissed
✅ StateFlow emission logs appear
✅ UI observers process state changes
✅ Both screens stay synchronized
✅ No infinite loops or listener issues
