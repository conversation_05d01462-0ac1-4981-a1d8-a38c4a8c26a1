<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <ScrollView
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginVertical="@dimen/_12sdp">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:animateLayoutChanges="true"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/indent_top"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical" />

                <LinearLayout
                    android:id="@+id/main"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="9dp"
                    android:layout_marginTop="3dp"
                    android:layout_marginEnd="9dp"
                    android:animateLayoutChanges="true"
                    android:background="@drawable/white_block"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/textView4"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="14dp"
                        android:layout_marginTop="7dp"
                        android:layout_marginEnd="14dp"
                        android:ellipsize="marquee"
                        android:focusableInTouchMode="true"
                        android:gravity="start"
                        android:marqueeRepeatLimit="marquee_forever"
                        android:singleLine="true"
                        android:text="@string/calibration_going"
                        android:textColor="?attr/black"
                        android:textSize="22sp" />

                    <LinearLayout
                        android:id="@+id/text5"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="7dp"
                        android:layout_marginEnd="8dp"
                        android:layout_marginBottom="1dp"
                        android:background="@drawable/grey_block"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="9dp"
                            android:layout_marginTop="7dp"
                            android:layout_marginEnd="7dp"
                            android:layout_marginBottom="7dp"
                            android:text="@string/disconnect_charge"
                            android:textColor="?attr/black"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="7dp"
                        android:layout_marginEnd="8dp"
                        android:layout_marginBottom="8dp"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/grey_block_line_up"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:layout_marginTop="12dp"
                                android:layout_marginBottom="12dp"
                                android:text="@string/device"
                                android:textColor="?attr/black"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/device_name"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="4dp"
                                android:layout_marginTop="12dp"
                                android:layout_marginEnd="10dp"
                                android:layout_marginBottom="12dp"
                                android:layout_weight="1"
                                android:ellipsize="marquee"
                                android:focusableInTouchMode="true"
                                android:marqueeRepeatLimit="marquee_forever"
                                android:singleLine="true"
                                android:text="@string/not_identified"
                                android:textAlignment="viewEnd"
                                android:textColor="?attr/black"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:background="@drawable/grey_block_line"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:layout_marginTop="12dp"
                                android:layout_marginBottom="12dp"
                                android:text="@string/discharging"
                                android:textColor="?attr/black"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/discharging"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="4dp"
                                android:layout_marginTop="12dp"
                                android:layout_marginEnd="10dp"
                                android:layout_marginBottom="12dp"
                                android:layout_weight="1"
                                android:ellipsize="marquee"
                                android:focusableInTouchMode="true"
                                android:marqueeRepeatLimit="marquee_forever"
                                android:singleLine="true"
                                android:text="@string/not_identified"
                                android:textAlignment="viewEnd"
                                android:textColor="?attr/black"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginTop="5dp"
                            android:background="@drawable/grey_block_line"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:layout_marginTop="12dp"
                                android:layout_marginBottom="12dp"
                                android:text="@string/polarity"
                                android:textColor="?attr/black"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/polarity"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="4dp"
                                android:layout_marginTop="12dp"
                                android:layout_marginEnd="10dp"
                                android:layout_marginBottom="12dp"
                                android:layout_weight="1"
                                android:ellipsize="marquee"
                                android:focusableInTouchMode="true"
                                android:marqueeRepeatLimit="marquee_forever"
                                android:singleLine="true"
                                android:text="@string/not_identified"
                                android:textAlignment="viewEnd"
                                android:textColor="?attr/black"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginTop="5dp"
                            android:background="@drawable/grey_block_line"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:layout_marginTop="12dp"
                                android:layout_marginBottom="12dp"
                                android:text="@string/measurement_parameter"
                                android:textColor="?attr/black"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/parameter"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="4dp"
                                android:layout_marginTop="12dp"
                                android:layout_marginEnd="10dp"
                                android:layout_marginBottom="12dp"
                                android:layout_weight="1"
                                android:ellipsize="marquee"
                                android:focusableInTouchMode="true"
                                android:marqueeRepeatLimit="marquee_forever"
                                android:singleLine="true"
                                android:text="@string/not_identified"
                                android:textAlignment="viewEnd"
                                android:textColor="?attr/black"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginTop="5dp"
                            android:background="@drawable/grey_block_line_down"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:layout_marginTop="12dp"
                                android:layout_marginBottom="12dp"
                                android:text="@string/desing_capacity"
                                android:textColor="?attr/black"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/capacity"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="4dp"
                                android:layout_marginTop="12dp"
                                android:layout_marginEnd="10dp"
                                android:layout_marginBottom="12dp"
                                android:layout_weight="1"
                                android:ellipsize="marquee"
                                android:focusableInTouchMode="true"
                                android:marqueeRepeatLimit="marquee_forever"
                                android:singleLine="true"
                                android:text="@string/not_identified"
                                android:textAlignment="viewEnd"
                                android:textColor="?attr/black"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="9dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="9dp"
                    android:layout_marginBottom="10dp"
                    android:animateLayoutChanges="true"
                    android:background="@drawable/white_block"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="8dp"
                        android:layout_marginBottom="8dp"
                        android:background="@drawable/grey_block"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="9dp"
                            android:layout_marginTop="7dp"
                            android:layout_marginEnd="7dp"
                            android:layout_marginBottom="7dp"
                            android:text="@string/design_capacity_text_incorrect"
                            android:textAlignment="viewStart"
                            android:textColor="?attr/black"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="8dp"
                        android:layout_marginEnd="8dp"
                        android:layout_marginBottom="8dp"
                        android:gravity="center">

                        <Button
                            android:id="@+id/change_capacity"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@+id/text_view_reset_charge"
                            android:layout_alignBottom="@+id/text_view_reset_charge"
                            android:layout_alignParentBottom="false"
                            android:background="@drawable/grey_block"
                            android:gravity="top|center_horizontal"
                            android:longClickable="false"
                            android:minWidth="0dp"
                            android:minHeight="0dp" />

                        <TextView
                            android:id="@+id/text_view_reset_charge"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:gravity="center"
                            android:paddingStart="10dp"
                            android:paddingTop="12.5dp"
                            android:paddingEnd="10dp"
                            android:paddingBottom="12.5dp"
                            android:text="@string/change_design_capacity"
                            android:textColor="?attr/black"
                            android:textSize="14sp" />
                    </RelativeLayout>
                </LinearLayout>


            </LinearLayout>

        </RelativeLayout>
    </ScrollView>

    <LinearLayout
        android:layout_gravity="end"
        android:id="@+id/next_page"
        android:layout_width="80dp"
        android:layout_height="40dp"
        android:layout_marginBottom="25dp"
        android:layout_marginEnd="30dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:outlineProvider="background"
        android:stateListAnimator="@null"
        android:visibility="visible">

        <TextView
            android:id="@+id/swipe_text"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_16sdp"
            android:layout_marginEnd="@dimen/_3sdp"
            android:text="@string/next"
            android:textAllCaps="true"
            android:textColor="?attr/black"
            android:textSize="@dimen/_14ssp"
            android:textStyle="bold" />

        <ImageView
            android:layout_width="@dimen/_12sdp"
            android:layout_height="@dimen/_12sdp"
            android:contentDescription="@string/next"
            android:scaleX="-1"
            android:src="@drawable/ic_strelka" />
    </LinearLayout>
    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/nativeAd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/grey" />
</LinearLayout>
