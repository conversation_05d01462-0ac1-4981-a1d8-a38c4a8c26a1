<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/grey">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@drawable/white_block"
        android:visibility="visible"
        android:paddingTop="@dimen/_8sdp"
        android:paddingHorizontal="@dimen/_8sdp"
        android:paddingBottom="@dimen/_65sdp"
        android:layout_marginTop="@dimen/_14sdp"
        android:layout_marginHorizontal="@dimen/_9sdp"
        android:layout_marginBottom="@dimen/_9sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:id="@+id/animation_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/animation"
                android:layout_weight="1"
                android:layout_marginStart="2dp"/>
            <ImageView
                android:id="@+id/animation_info"
                android:visibility="visible"
                android:layout_width="22sp"
                android:layout_height="match_parent"
                android:src="@drawable/ic_note"
                android:scaleType="fitEnd"
                android:layout_marginStart="5dp"/>
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/categoryRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginTop="8dp"
            android:clipToPadding="false"
            android:orientation="horizontal"
            android:overScrollMode="never"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/animationRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:layout_marginTop="5dp"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="2"/>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>