<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.media3.ui.PlayerView
        android:id="@+id/playerView"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:background="?attr/black"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
    <!-- Overlay làm mờ video -->
    <View
        android:id="@+id/blurOverlay"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#80000000"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent" />
<LinearLayout
    android:orientation="vertical"
    android:gravity="top"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/include_back_navigation"
        layout="@layout/layout_back_navigation" />
    <LinearLayout
        android:orientation="vertical"
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
    <TextView
        android:textColor="?attr/white"
        android:id="@+id/titleText"
        android:text="@string/animation_applied_title"
        android:textSize="22sp"
        android:textStyle="bold"
        android:layout_gravity="center"
        android:layout_marginBottom="16dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottieView"
            android:layout_width="200dp"
            android:layout_height="200dp"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/lottie_json" />


    <TextView
        android:textColor="?attr/white"
        android:textStyle="bold"
        android:id="@+id/descriptionText"
        android:text="@string/animation_applied_message"
        android:textAlignment="center"
        android:layout_marginHorizontal="@dimen/_32sdp"
        android:layout_marginBottom="32dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />





            <LinearLayout
                android:layout_gravity="start"
                android:layout_marginStart="10dp"
                android:orientation="horizontal"
                android:id="@+id/switch_anti_thief_block"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="visible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:gravity="start"
                    android:layout_marginStart="10dp"
                    android:textStyle="italic"
                    android:id="@+id/switch_anti_thief_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:rotation="0"
                    android:singleLine="true"
                    android:text="@string/about_anti_thief"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/white"
                    android:textSize="@dimen/_16ssp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/anti_thief_info"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </LinearLayout>

        <LinearLayout
            android:layout_gravity="end"
            android:id="@+id/next_page"
            android:visibility="visible"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="50dp"
            android:layout_marginEnd="30dp"
            android:stateListAnimator="@null"
            android:gravity="center"
            android:orientation="horizontal"
            android:outlineProvider="background">
            <TextView
                android:id="@+id/swipe_text"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_16sdp"
                android:textStyle="bold"
                android:textSize="@dimen/_14ssp"
                android:layout_marginEnd="@dimen/_3sdp"
                android:textAllCaps="true"
                android:textColor="?attr/white"
                android:text="Others"/>
            <ImageView
                android:layout_width="@dimen/_12sdp"
                android:layout_height="@dimen/_12sdp"
                android:src="@drawable/ic_strelka"
                android:contentDescription="@string/next"
                android:scaleX="-1"/>
        </LinearLayout>


    </LinearLayout>
</LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
