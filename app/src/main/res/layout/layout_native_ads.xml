<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/max_native_ad_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="12dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/_50sdp"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/icon_image_view"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:scaleType="centerCrop"
            android:layout_marginEnd="8dp"
            android:background="@drawable/icon_background"/>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_weight="1">

            <TextView
                android:id="@+id/title_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="App Title"
                android:textStyle="bold"
                android:textSize="16sp"
                android:textColor="?attr/black"/>

            <TextView
                android:id="@+id/advertiser_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Advertiser"
                android:textSize="12sp"
                android:textColor="?attr/black"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ad_options_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"/>
    </LinearLayout>

    <TextView
        android:id="@+id/body_text_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="This is the ad description."
        android:textColor="?attr/black"
        android:textSize="14sp"
        android:layout_marginBottom="8dp"/>

    <FrameLayout
        android:id="@+id/media_view_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_120sdp"
        android:layout_marginBottom="8dp" />

    <FrameLayout
        android:id="@+id/star_rating_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="0dp" />

    <!-- CTA Button -->
    <Button
        android:id="@+id/cta_button"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:text="INSTALL"
        android:textAllCaps="true"
        android:textColor="?attr/black"
        android:background="@drawable/white_block_ads"
        android:textStyle="bold"/>
</LinearLayout>
