<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/grey">

    <!-- Toolbar/ActionBar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:layout_alignParentTop="true"
        android:background="?attr/grey"
        android:elevation="4dp"
        app:navigationIcon="@drawable/ic_back"
        app:title="@string/accessibility_guide_title"
        app:titleTextColor="?attr/black"/>

    <!-- Scrollable Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar"
        android:layout_above="@id/bottom_button_container"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Step 1 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="24dp">

                <TextView
                    android:id="@+id/step1_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/accessibility_guide_step1"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?attr/black"
                    android:layout_marginBottom="12dp"/>

                <ImageView
                    android:id="@+id/step1_image"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:layout_marginBottom="8dp"
                    android:scaleType="centerInside"
                    android:background="@drawable/grey_block"
                    android:padding="8dp"
                    android:src="@drawable/accessibility_step1"
                    android:contentDescription="@string/accessibility_guide_step1"/>
            </LinearLayout>

            <!-- Step 2 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="24dp">

                <TextView
                    android:id="@+id/step2_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/accessibility_guide_step2"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?attr/black"
                    android:layout_marginBottom="12dp"/>

                <ImageView
                    android:id="@+id/step2_image"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:layout_marginBottom="8dp"
                    android:scaleType="centerInside"
                    android:background="@drawable/grey_block"
                    android:padding="8dp"
                    android:src="@drawable/accessibility_step2"
                    android:contentDescription="@string/accessibility_guide_step2"/>
            </LinearLayout>

            <!-- Step 3 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="24dp">

                <TextView
                    android:id="@+id/step3_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/accessibility_guide_step3"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?attr/black"
                    android:layout_marginBottom="12dp"/>

                <ImageView
                    android:id="@+id/step3_image"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:layout_marginBottom="8dp"
                    android:scaleType="centerInside"
                    android:background="@drawable/grey_block"
                    android:padding="8dp"
                    android:src="@drawable/accessibility_step3"
                    android:contentDescription="@string/accessibility_guide_step3"/>
            </LinearLayout>

        </LinearLayout>
    </ScrollView>

    <!-- Bottom Button Container -->
    <LinearLayout
        android:id="@+id/bottom_button_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="?attr/grey">

        <!-- Go to Setting Button -->
        <Button
            android:id="@+id/go_to_setting_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/go_to_setting"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:background="@drawable/gradient_button_background"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:layout_marginTop="8dp"/>
    </LinearLayout>

</RelativeLayout>
