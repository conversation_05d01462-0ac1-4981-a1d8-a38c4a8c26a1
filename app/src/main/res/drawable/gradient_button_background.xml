<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#2A80F5"
                android:endColor="#34C7F8"
                android:angle="0"
                android:type="linear"/>
            <corners android:radius="8dp"/>
        </shape>
    </item>
    
    <!-- Disabled state -->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="?attr/unselected_button"/>
            <corners android:radius="8dp"/>
        </shape>
    </item>
    
    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#34C7F8"
                android:endColor="#2A80F5"
                android:angle="0"
                android:type="linear"/>
            <corners android:radius="8dp"/>
        </shape>
    </item>
    
</selector>
