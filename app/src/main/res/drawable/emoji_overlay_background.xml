<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">
    
    <!-- Solid background that matches status bar -->
    <solid android:color="@color/emoji_overlay_background_light" />
    
    <!-- Subtle border for definition -->
    <stroke
        android:width="0.5dp"
        android:color="@color/emoji_overlay_background_stroke" />
    
    <!-- Rounded corners for modern look -->
    <corners android:radius="8dp" />
    
    <!-- Padding for content -->
    <padding
        android:left="6dp"
        android:top="2dp"
        android:right="6dp"
        android:bottom="2dp" />
        
</shape>
