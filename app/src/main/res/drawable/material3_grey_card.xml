<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="?attr/grey_pressed" />
            <corners android:radius="12dp" />
            <stroke 
                android:width="1dp" 
                android:color="?attr/grey_lighter" />
        </shape>
    </item>
    
    <!-- Default state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="?attr/grey_lighter" />
            <corners android:radius="12dp" />
            <stroke 
                android:width="1dp" 
                android:color="?attr/grey_lighter" />
        </shape>
    </item>
</selector>
