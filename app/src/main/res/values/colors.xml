<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="empty">#00ffffff</color>
    <color name="green">#109d58</color>
    <color name="green_lighter">#85109d58</color>
    <color name="progress_charging">#4CAF50</color>
    <color name="progress_discharging">#F44336</color>
    <color name="blue">#8744ff</color>
    <color name="orange">#ff6e40</color>
    <color name="light_blue">#a2d5c6</color>
    <color name="red">#ff5151</color>
    <color name="pink">#d9a5b3</color>
    <color name="light_green">#a8c66c</color>
    <color name="telo">#ecc19c</color>
    <color name="gold">#f3ca20</color>
    <color name="night_blue">#3b4d61</color>
    <color name="color1">#6b8e23</color>
    <color name="color2">#87cefa</color>
    <color name="color3">#da70d6</color>
    <color name="color4">#00ff00</color>
    <color name="color5">#00ffff</color>
    <color name="color6">#ffa500</color>
    <color name="color7">#ff1493</color>
    <color name="color8">#ff0000</color>
    <color name="color9">#0000ff</color>
    <color name="color10">#a0522d</color>
    <color name="white">#FFFFFF</color>

    <!-- Material 3 Surface Colors for Cards -->
    <color name="material3_surface_light">#FFFBFE</color>
    <color name="material3_surface_variant_light">#F4EFF4</color>
    <color name="material3_outline_light">#79747E</color>
    <color name="material3_outline_variant_light">#CAC4D0</color>

    <!-- Grey Card Colors for Different Themes -->
    <color name="grey_card_light">#F5F5F5</color>
    <color name="grey_card_pressed_light">#EEEEEE</color>
    <color name="grey_card_dark">#2C2C2C</color>
    <color name="grey_card_pressed_dark">#3A3A3A</color>

    <!-- Category selection colors -->
    <color name="category_selected_text">#FFFFFF</color>
    <color name="category_unselected_text">#666666</color>

    <!-- Emoji Battery Overlay Colors -->
    <color name="emoji_battery_percentage">#FF6B9D</color>  <!-- Pink/red color from screenshot -->
    <color name="emoji_heart_icon">#FF4A7C</color>         <!-- Slightly darker pink for heart -->
    <color name="emoji_battery_outline">#000000</color>    <!-- Black outline for light theme -->
    <color name="emoji_text_color">#000000</color>         <!-- Black text for time and percentage -->
    <color name="emoji_overlay_background_light">#F5F5F5</color>  <!-- Light theme status bar background -->
    <color name="emoji_overlay_background_dark">#1C1C1E</color>   <!-- Dark theme status bar background -->
    <color name="emoji_overlay_background_stroke">#E0E0E0</color> <!-- Subtle border for definition -->

    <!-- Status Bar Icon Colors for Light Theme -->
    <color name="emoji_status_icon_color">#000000</color>  <!-- Black icons for light theme -->
    <color name="emoji_time_text_color">#000000</color>    <!-- Black time text for light theme -->

    <!-- Theme-aware colors for different overlay states -->
    <color name="emoji_overlay_text_primary">#000000</color>      <!-- Primary text color -->
    <color name="emoji_overlay_text_secondary">#666666</color>    <!-- Secondary text color -->
    <color name="emoji_overlay_icon_primary">#000000</color>      <!-- Primary icon color -->
    <color name="emoji_overlay_icon_secondary">#666666</color>    <!-- Secondary icon color -->

    <!-- Background colors for different light theme variants -->
    <color name="emoji_overlay_bg_white">#FFFFFF</color>          <!-- Pure white -->
    <color name="emoji_overlay_bg_light_grey">#F5F5F5</color>     <!-- Light grey -->
    <color name="emoji_overlay_bg_off_white">#FAFAFA</color>      <!-- Off-white variant -->

    <!-- Stroke/border colors for light theme -->
    <color name="emoji_overlay_stroke_light">#E0E0E0</color>      <!-- Light stroke -->
    <color name="emoji_overlay_stroke_medium">#CCCCCC</color>     <!-- Medium stroke -->
    <color name="emoji_overlay_stroke_dark">#999999</color>       <!-- Dark stroke -->

    <!-- Component Adapter Colors -->
    <color name="component_background_default">#FFFFFF</color>
    <color name="component_background_selected">#F0F8FF</color>
    <color name="component_border_default">#E0E0E0</color>
    <color name="component_border_selected">#2196F3</color>
    <color name="selection_indicator_overlay">#332196F3</color>
    <color name="shimmer_placeholder">#F0F0F0</color>
</resources>