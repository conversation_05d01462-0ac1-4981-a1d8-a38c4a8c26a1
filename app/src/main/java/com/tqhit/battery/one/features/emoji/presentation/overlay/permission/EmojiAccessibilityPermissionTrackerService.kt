package com.tqhit.battery.one.features.emoji.presentation.overlay.permission

import android.content.Context
import android.content.Intent
import android.util.Log
import android.view.accessibility.AccessibilityManager
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service for tracking emoji accessibility permission changes as requested by the user.
 * 
 * This service extends LifecycleService to provide lifecycle-aware permission tracking 
 * specifically for the emoji module. It monitors accessibility service permission changes
 * and emits status updates to subscribers.
 * 
 * Key Features:
 * - Lifecycle-aware permission tracking using LifecycleService
 * - Registers AccessibilityManager.AccessibilityStateChangeListener for emoji service monitoring
 * - Exposes StateFlow<Boolean> for reactive permission status updates
 * - Uses EmojiOverlayPermissionManager for consistent permission checking
 * - Comprehensive logging with emoji-specific tags for easy debugging
 * - Follows established DI patterns with @Singleton annotation
 * - Focuses specifically on emoji overlay permissions, not general app permissions
 * 
 * Usage:
 * - Start the service to begin tracking permission changes
 * - Observe permissionStatus StateFlow for reactive updates
 * - Service automatically handles lifecycle management
 * - Emits permission status changes when user returns from settings
 */
@Singleton
@AndroidEntryPoint
class EmojiAccessibilityPermissionTrackerService : LifecycleService() {

    companion object {
        private const val TAG = "EmojiPermissionTracker"
        private const val EMOJI_PERMISSION_TRACKER_TAG = "EmojiPermission_Tracker"
        private const val EMOJI_TRACKER_LIFECYCLE_TAG = "EmojiTracker_Lifecycle"
        
        // Service control methods
        fun startTracking(context: Context) {
            BatteryLogger.d(TAG, "Starting emoji permission tracking service")
            val intent = Intent(context, EmojiAccessibilityPermissionTrackerService::class.java)
            context.startService(intent)
        }
        
        fun stopTracking(context: Context) {
            BatteryLogger.d(TAG, "Stopping emoji permission tracking service")
            val intent = Intent(context, EmojiAccessibilityPermissionTrackerService::class.java)
            context.stopService(intent)
        }
    }

    // Permission status flow - emits true when emoji accessibility permission is granted
    private val _permissionStatus = MutableStateFlow(false)
    val permissionStatus: StateFlow<Boolean> = _permissionStatus.asStateFlow()

    // Accessibility service change listener
    private var accessibilityManager: AccessibilityManager? = null
    private var accessibilityStateChangeListener: AccessibilityManager.AccessibilityStateChangeListener? = null
    
    // Tracking state
    private var isTracking = false

    override fun onCreate() {
        super.onCreate()
        BatteryLogger.d(TAG, "EmojiAccessibilityPermissionTrackerService created")
        BatteryLogger.d(EMOJI_TRACKER_LIFECYCLE_TAG, "EMOJI_PERMISSION_TRACKER_SERVICE_CREATED")
        
        initializePermissionTracking()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        BatteryLogger.d(TAG, "Service onStartCommand called")
        BatteryLogger.d(EMOJI_TRACKER_LIFECYCLE_TAG, "EMOJI_PERMISSION_TRACKER_SERVICE_STARTED")
        
        startPermissionTracking()
        
        // Return START_STICKY to ensure service is restarted if killed
        return START_STICKY
    }

    override fun onDestroy() {
        BatteryLogger.d(TAG, "EmojiAccessibilityPermissionTrackerService destroyed")
        BatteryLogger.d(EMOJI_TRACKER_LIFECYCLE_TAG, "EMOJI_PERMISSION_TRACKER_SERVICE_DESTROYED")
        
        stopPermissionTracking()
        super.onDestroy()
    }

    /**
     * Initializes the permission tracking system.
     * Sets up AccessibilityManager and initial permission status.
     */
    private fun initializePermissionTracking() {
        try {
            BatteryLogger.d(TAG, "Initializing emoji permission tracking")
            
            accessibilityManager = getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
            
            // Set initial permission status
            updatePermissionStatus()
            
            BatteryLogger.d(EMOJI_PERMISSION_TRACKER_TAG, "EMOJI_PERMISSION_TRACKING_INITIALIZED")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to initialize emoji permission tracking: ${e.message}")
            Log.e(TAG, "Error initializing permission tracking", e)
        }
    }

    /**
     * Starts tracking accessibility permission changes for emoji service.
     */
    private fun startPermissionTracking() {
        if (isTracking) {
            BatteryLogger.d(TAG, "Emoji permission tracking already active")
            return
        }

        try {
            BatteryLogger.d(TAG, "Starting emoji accessibility permission tracking")
            
            // Create and register accessibility state change listener
            accessibilityStateChangeListener = AccessibilityManager.AccessibilityStateChangeListener { enabled ->
                BatteryLogger.d(EMOJI_PERMISSION_TRACKER_TAG, "EMOJI_ACCESSIBILITY_STATE_CHANGED: $enabled")
                handleAccessibilityStateChange()
            }
            
            accessibilityManager?.addAccessibilityStateChangeListener(accessibilityStateChangeListener!!)
            isTracking = true
            
            BatteryLogger.d(EMOJI_PERMISSION_TRACKER_TAG, "EMOJI_PERMISSION_TRACKING_STARTED")
            Log.d(TAG, "Emoji accessibility permission tracking started successfully")
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to start emoji permission tracking: ${e.message}")
            Log.e(TAG, "Error starting permission tracking", e)
        }
    }

    /**
     * Stops tracking accessibility permission changes.
     */
    private fun stopPermissionTracking() {
        if (!isTracking) {
            BatteryLogger.d(TAG, "Emoji permission tracking not active")
            return
        }

        try {
            BatteryLogger.d(TAG, "Stopping emoji accessibility permission tracking")
            
            // Remove accessibility state change listener
            accessibilityStateChangeListener?.let { listener ->
                accessibilityManager?.removeAccessibilityStateChangeListener(listener)
            }
            
            accessibilityStateChangeListener = null
            isTracking = false
            
            BatteryLogger.d(EMOJI_PERMISSION_TRACKER_TAG, "EMOJI_PERMISSION_TRACKING_STOPPED")
            Log.d(TAG, "Emoji accessibility permission tracking stopped")
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to stop emoji permission tracking: ${e.message}")
            Log.e(TAG, "Error stopping permission tracking", e)
        }
    }

    /**
     * Handles accessibility state changes and updates permission status.
     * Called when the accessibility service state changes in the system.
     */
    private fun handleAccessibilityStateChange() {
        lifecycleScope.launch {
            try {
                BatteryLogger.d(TAG, "Handling emoji accessibility state change")
                updatePermissionStatus()
                BatteryLogger.d(EMOJI_PERMISSION_TRACKER_TAG, "EMOJI_ACCESSIBILITY_STATE_CHANGE_HANDLED")
            } catch (e: Exception) {
                BatteryLogger.e(TAG, "Failed to handle emoji accessibility state change: ${e.message}")
                Log.e(TAG, "Error handling accessibility state change", e)
            }
        }
    }

    /**
     * Updates the current permission status and emits to subscribers.
     * Uses EmojiOverlayPermissionManager for consistent permission checking.
     */
    private fun updatePermissionStatus() {
        try {
            val currentStatus = EmojiOverlayPermissionManager.isAccessibilityServiceEnabled(this)
            val previousStatus = _permissionStatus.value
            
            BatteryLogger.d(TAG, "Updating emoji permission status: $previousStatus -> $currentStatus")
            
            if (currentStatus != previousStatus) {
                _permissionStatus.value = currentStatus
                
                BatteryLogger.d(EMOJI_PERMISSION_TRACKER_TAG, "EMOJI_PERMISSION_STATUS_CHANGED: $currentStatus")
                Log.d(TAG, "Emoji permission status changed to: $currentStatus")
                
                // Log detailed permission status for debugging
                val permissionSummary = EmojiOverlayPermissionManager.getPermissionStatusSummary(this)
                BatteryLogger.d(EMOJI_PERMISSION_TRACKER_TAG, "EMOJI_PERMISSION_SUMMARY: $permissionSummary")
            } else {
                BatteryLogger.d(TAG, "Emoji permission status unchanged: $currentStatus")
            }
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to update emoji permission status: ${e.message}")
            Log.e(TAG, "Error updating permission status", e)
        }
    }

    /**
     * Gets the current permission status synchronously.
     * Useful for immediate status checks without observing the flow.
     * 
     * @return true if emoji accessibility permission is granted, false otherwise
     */
    fun getCurrentPermissionStatus(): Boolean {
        return _permissionStatus.value
    }

    /**
     * Forces a permission status check and update.
     * Useful when returning from settings or when manual refresh is needed.
     */
    fun refreshPermissionStatus() {
        BatteryLogger.d(TAG, "Manually refreshing emoji permission status")
        updatePermissionStatus()
    }
}
