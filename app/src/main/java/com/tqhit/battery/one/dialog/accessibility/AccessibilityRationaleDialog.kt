package com.tqhit.battery.one.dialog.accessibility

import android.content.Context
import android.content.DialogInterface
import android.graphics.Color
import android.util.Log
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.graphics.drawable.toDrawable
import com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.DialogAccessibilityRationaleBinding
import com.tqhit.battery.one.utils.BatteryLogger


/**
 * Dialog that shows accessibility service usage rationale with checkbox agreement.
 * This is the first step in the enhanced accessibility permission flow.
 * 
 * Features:
 * - Clear explanation of accessibility service usage
 * - Checkbox agreement requirement before proceeding
 * - Proper button state management (disabled/enabled)
 * - Comprehensive logging for user interactions
 * - Follows existing app dialog patterns and theming
 */
class AccessibilityRationaleDialog(
    private val context: Context,
    private val onNext: () -> Unit = {},
    private val onClose: () -> Unit = {}
) : AdLibBaseDialog<DialogAccessibilityRationaleBinding>(context) {

    companion object {
        private const val TAG = "AccessibilityRationaleDialog"
        private const val ACCESSIBILITY_DIALOG_TAG = "ACCESSIBILITY_DIALOG"
    }

    override val binding by lazy { DialogAccessibilityRationaleBinding.inflate(layoutInflater) }

    override fun initWindow() {
        BatteryLogger.d(TAG, "Initializing accessibility rationale dialog window")
        
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        val layoutParams = WindowManager.LayoutParams()
        layoutParams.copyFrom(window?.attributes)
        window?.attributes = layoutParams
        
        // Prevent dismissal by touching outside but allow back button
        setCanceledOnTouchOutside(false)
        setCancelable(true)

        // Handle back button press as close action
        setOnCancelListener {
            BatteryLogger.d(ACCESSIBILITY_DIALOG_TAG, "ACCESSIBILITY_RATIONALE_DIALOG_CANCELLED_BY_BACK_BUTTON")
            handleCloseAction()
        }
        
        BatteryLogger.d(ACCESSIBILITY_DIALOG_TAG, "ACCESSIBILITY_RATIONALE_DIALOG_WINDOW_INITIALIZED")
    }

    override fun setupUI() {
        try {
            BatteryLogger.d(TAG, "Setting up accessibility rationale dialog UI")
            
            // Call super.setupUI() with error handling for ad-related failures
            try {
                super.setupUI()
            } catch (e: Exception) {
                Log.w(TAG, "Error in super.setupUI() - likely ad initialization failure, continuing with basic setup", e)
            }

            // Set title and message text from string resources
            binding.titleText.text = context.getString(R.string.accessibility_service_usage_title)
            binding.messageText.text = context.getString(R.string.accessibility_service_usage_message)
            binding.agreementText.text = context.getString(R.string.i_agree)
            binding.closeTextButton.text = context.getString(R.string.accessibility_rationale_close)
            binding.nextButton.text = context.getString(R.string.accessibility_rationale_next)
            
            // Initially disable the Next button
            updateNextButtonState(false)
            
            BatteryLogger.d(ACCESSIBILITY_DIALOG_TAG, "ACCESSIBILITY_RATIONALE_DIALOG_UI_SETUP_COMPLETED")
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up accessibility rationale dialog UI", e)
            BatteryLogger.e(TAG, "Failed to setup accessibility rationale dialog UI: ${e.message}")
        }
    }

    override fun setupListener() {
        try {
            BatteryLogger.d(TAG, "Setting up accessibility rationale dialog listeners")
            
            super.setupListener()
            
            // Checkbox listener to enable/disable Next button
            binding.agreementCheckbox.setOnCheckedChangeListener { _, isChecked ->
                BatteryLogger.d(ACCESSIBILITY_DIALOG_TAG, "ACCESSIBILITY_RATIONALE_CHECKBOX_CHANGED: $isChecked")
                updateNextButtonState(isChecked)
            }
            
            // Close button listeners (both X button and text button)
            binding.closeButton.setOnClickListener {
                BatteryLogger.d(ACCESSIBILITY_DIALOG_TAG, "ACCESSIBILITY_RATIONALE_CLOSE_BUTTON_CLICKED")
                handleCloseAction()
            }
            
            binding.closeTextButton.setOnClickListener {
                BatteryLogger.d(ACCESSIBILITY_DIALOG_TAG, "ACCESSIBILITY_RATIONALE_CLOSE_TEXT_BUTTON_CLICKED")
                handleCloseAction()
            }
            
            // Next button listener
            binding.nextButton.setOnClickListener {
                if (binding.agreementCheckbox.isChecked) {
                    BatteryLogger.d(ACCESSIBILITY_DIALOG_TAG, "ACCESSIBILITY_RATIONALE_NEXT_BUTTON_CLICKED")
                    handleNextAction()
                } else {
                    BatteryLogger.w(TAG, "Next button clicked but checkbox not checked")
                }
            }
            
            BatteryLogger.d(ACCESSIBILITY_DIALOG_TAG, "ACCESSIBILITY_RATIONALE_DIALOG_LISTENERS_SETUP_COMPLETED")
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up accessibility rationale dialog listeners", e)
            BatteryLogger.e(TAG, "Failed to setup accessibility rationale dialog listeners: ${e.message}")
        }
    }

    /**
     * Updates the Next button state based on checkbox agreement
     */
    private fun updateNextButtonState(isEnabled: Boolean) {
        binding.nextButton.isEnabled = isEnabled
        
        // Update button appearance based on state
        if (isEnabled) {
            binding.nextButton.setBackgroundResource(R.drawable.gradient_button_background)
            binding.nextButton.setTextColor(context.getColor(android.R.color.white))
        } else {
            binding.nextButton.setBackgroundResource(R.drawable.grey_block_line_up)
            // Use theme attribute for unselected button color
            val typedArray = context.theme.obtainStyledAttributes(intArrayOf(R.attr.unselected_button))
            val unselectedColor = typedArray.getColor(0, context.getColor(android.R.color.darker_gray))
            typedArray.recycle()
            binding.nextButton.setTextColor(unselectedColor)
        }
        
        BatteryLogger.d(TAG, "Next button state updated: enabled=$isEnabled")
    }

    /**
     * Handles close action (dismiss dialog and call onClose callback)
     */
    private fun handleCloseAction() {
        try {
            BatteryLogger.d(TAG, "Handling close action for accessibility rationale dialog")
            dismiss()
            onClose()
        } catch (e: Exception) {
            Log.e(TAG, "Error handling close action", e)
            BatteryLogger.e(TAG, "Failed to handle close action: ${e.message}")
        }
    }

    /**
     * Handles next action (dismiss dialog and call onNext callback)
     */
    private fun handleNextAction() {
        try {
            BatteryLogger.d(TAG, "Handling next action for accessibility rationale dialog")
            dismiss()
            onNext()
        } catch (e: Exception) {
            Log.e(TAG, "Error handling next action", e)
            BatteryLogger.e(TAG, "Failed to handle next action: ${e.message}")
        }
    }
}
