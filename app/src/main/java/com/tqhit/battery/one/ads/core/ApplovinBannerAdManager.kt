package com.tqhit.battery.one.ads.core

import android.os.Handler
import android.os.Looper
import android.view.ViewGroup
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdListener
import com.applovin.mediation.MaxAdRevenueListener
import com.applovin.mediation.MaxAdViewAdListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxAdView
import com.google.firebase.analytics.FirebaseAnalytics
import com.tqhit.adlib.sdk.analytics.AnalyticsTracker
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ApplovinBannerAdManager @Inject constructor(
    private val analyticsTracker: AnalyticsTracker
) : MaxAdRevenueListener, MaxAdViewAdListener {
    private val adUnitId = "d25a1beaccf7bd55"
    private val waitTime = 1000L
    private val maxRetryCount = 6

    private var listener : MaxAdViewAdListener? = null
    private val placementImpressions = mutableMapOf<String, MutableList<MaxAd>>()
    private val lastLogTimes = mutableMapOf<String, Long>()
    private val LOG_INTERVAL = 180_000L // 180 seconds in milliseconds
    private val handler = Handler(Looper.getMainLooper())
    private val timerRunnable = object : Runnable {
        override fun run() {
            checkAndLogByTime()
            handler.postDelayed(this, 5_000L) // check every 5 seconds
        }
    }

    init {
        handler.postDelayed(timerRunnable, 5_000L)
    }

    fun createAd(): MaxAdView {
        val adView = MaxAdView(adUnitId)
        adView.layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        return adView
    }

    fun loadAd(
        placement: String,
        adView: MaxAdView,
        listener: MaxAdViewAdListener,
    ) {
        this.listener = listener
        adView.setListener(this)
        adView.setRevenueListener(this)
        adView.placement = placement
        adView.loadAd()
        analyticsTracker.logEvent("banner_load", mapOf("placement" to placement))
    }

    fun destroy(
        adView: MaxAdView
    ) {
        adView.destroy()
    }

    private fun logPlacement(placement: String, impressions: List<MaxAd>) {
        val totalRevenue = impressions.sumOf { it.revenue }
        val impressionCount = impressions.size
        val adUnitId = impressions.firstOrNull()?.adUnitId ?: ""
        val format = impressions.firstOrNull()?.format?.label ?: ""
        val networkName = impressions.firstOrNull()?.networkName ?: ""
        analyticsTracker.logEvent(
            FirebaseAnalytics.Event.AD_IMPRESSION,
            mapOf(
                FirebaseAnalytics.Param.AD_PLATFORM to "appLovin",
                FirebaseAnalytics.Param.AD_UNIT_NAME to adUnitId,
                FirebaseAnalytics.Param.AD_FORMAT to format,
                FirebaseAnalytics.Param.AD_SOURCE to networkName,
                FirebaseAnalytics.Param.VALUE to totalRevenue,
                FirebaseAnalytics.Param.CURRENCY to "USD",
                "placement" to placement,
                "impression_count" to impressionCount,
            )
        )
        analyticsTracker.logEvent(
            "ad_impression_custom",
            mapOf(
                FirebaseAnalytics.Param.AD_PLATFORM to "appLovin",
                FirebaseAnalytics.Param.AD_UNIT_NAME to adUnitId,
                FirebaseAnalytics.Param.AD_FORMAT to format,
                FirebaseAnalytics.Param.AD_SOURCE to networkName,
                FirebaseAnalytics.Param.VALUE to totalRevenue,
                FirebaseAnalytics.Param.CURRENCY to "USD",
                "placement" to placement,
                "impression_count" to impressionCount,
            )
        )
        // Update last log time
        lastLogTimes[placement] = System.currentTimeMillis()
    }

    private fun checkAndLogByTime() {
        val now = System.currentTimeMillis()
        val placementsToLog = placementImpressions.filter { (placement, impressions) ->
            impressions.isNotEmpty() &&
            (now - (lastLogTimes[placement] ?: 0L) >= LOG_INTERVAL)
        }.keys
        for (placement in placementsToLog) {
            val impressions = placementImpressions[placement] ?: continue
            logPlacement(placement, impressions)
            placementImpressions[placement]?.clear()
        }
    }

    override fun onAdRevenuePaid(impressionData: MaxAd) {
        val placement = impressionData.placement
        val impressions = placementImpressions.getOrPut(placement) { mutableListOf() }
        impressions.add(impressionData)
        // If this placement has 5 or more impressions, log and clear
        if (impressions.size >= 5) {
            logPlacement(placement, impressions)
            impressions.clear()
        }
    }

    override fun onAdLoaded(ad: MaxAd) {
        analyticsTracker.logEvent("banner_load_success", mapOf("placement" to ad.placement))
        listener?.onAdLoaded(ad)
    }

    override fun onAdLoadFailed(p0: String, p1: MaxError) {
        analyticsTracker.logEvent("banner_load_fail")
        listener?.onAdLoadFailed(p0, p1)
    }

    override fun onAdDisplayed(ad: MaxAd) {
        analyticsTracker.logEvent("banner_show_success", mapOf("placement" to ad.placement))
        listener?.onAdDisplayed(ad)
    }

    override fun onAdDisplayFailed(ad: MaxAd, p1: MaxError) {
        analyticsTracker.logEvent("banner_show_fail", mapOf("placement" to ad.placement))
        listener?.onAdDisplayFailed(ad, p1)
    }

    override fun onAdExpanded(p0: MaxAd) {
        listener?.onAdExpanded(p0)
    }

    override fun onAdCollapsed(p0: MaxAd) {
        listener?.onAdCollapsed(p0)
    }

    override fun onAdHidden(ad: MaxAd) {
        analyticsTracker.logEvent("banner_close", mapOf("placement" to ad.placement))
        listener?.onAdHidden(ad)
    }

    override fun onAdClicked(ad: MaxAd) {
        analyticsTracker.logEvent("banner_click", mapOf("placement" to ad.placement))
        listener?.onAdClicked(ad)
    }

    fun calculateWaitTime(
        retryCount: Int
    ): Long {
        if (retryCount < 0 || retryCount > maxRetryCount) {
            return -1L
        }
        return waitTime * (1 shl retryCount)
    }
}