I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end. Make sure you fix all the linting, compilation or validation issues after successful implementation of the plan.

### Observations

Based on my exploration, I found the two emoji overlay toggles that need to be unified:

1. **EmojiBatteryFragment.kt** (Gallery screen) - has `globalToggleSwitch` that calls `BatteryGalleryEvent.ToggleGlobalFeature(isChecked)`
2. **EmojiCustomizeActivity.kt** (Customization screen) - has `globalToggleSwitch` that calls `CustomizeEvent.ToggleGlobalEnabled(isChecked)`

Both toggles control the same emoji overlay functionality but use different ViewModels and event handling. The existing `EmojiOverlayPermissionManager.kt` provides robust permission handling with dialogs and guide activities. The current architecture has separate state management for each screen, but they should share the same permission flow and toggle behavior as requested by the user.

### Approach

The solution involves creating a unified permission tracking service and a shared toggle manager specifically for the emoji module. We'll leverage the existing `EmojiOverlayPermissionManager` as the foundation and create a centralized service to monitor accessibility permission changes. Both toggles will use the same permission flow: check permissions → show dialog if needed → auto-update toggle when user returns with granted permissions. The approach ensures consistent UX across both emoji screens while maintaining the existing architecture patterns.

### Reasoning

I explored the emoji module codebase and found the two toggles mentioned by the user. I examined the current permission handling in `EmojiOverlayPermissionManager.kt` and identified that while robust permission flows exist, they're not consistently applied across both toggles. I also confirmed the emoji module structure from the provided summary document and located the specific toggle implementations in both the gallery fragment and customization activity.

## Mermaid Diagram

sequenceDiagram
    participant User
    participant GalleryToggle as "Gallery Toggle (EmojiBatteryFragment)"
    participant CustomizeToggle as "Customize Toggle (EmojiCustomizeActivity)"
    participant UnifiedManager as "EmojiUnifiedToggleManager"
    participant PermissionMgr as "EmojiOverlayPermissionManager"
    participant Tracker as "EmojiAccessibilityPermissionTrackerService"
    participant Dialog as "Accessibility Dialog"
    participant Settings as "System Settings"

    User->>GalleryToggle: Tap toggle to enable
    GalleryToggle->>UnifiedManager: handleEmojiToggleChange(true, context)
    UnifiedManager->>PermissionMgr: hasAllRequiredPermissions()
    
    alt Permission already granted
        PermissionMgr-->>UnifiedManager: true
        UnifiedManager->>GalleryToggle: onToggleEnabled()
        UnifiedManager->>CustomizeToggle: Sync toggle state
        GalleryToggle->>User: Toggle shows enabled + emoji overlay active
    else Permission not granted
        PermissionMgr-->>UnifiedManager: false
        UnifiedManager->>PermissionMgr: showAccessibilityPermissionFlow()
        PermissionMgr->>Dialog: Show rationale dialog
        Dialog->>User: "Allow accessibility access for emoji overlay?"
        
        alt User continues
            User->>Dialog: Continue
            Dialog->>Settings: Open accessibility settings
            User->>Settings: Grant emoji accessibility permission
            Settings->>Tracker: Permission status changed
            Tracker->>UnifiedManager: Permission granted notification
            UnifiedManager->>GalleryToggle: onToggleEnabled()
            UnifiedManager->>CustomizeToggle: Sync toggle state
            GalleryToggle->>User: Auto-enable toggle + show emoji overlay
        else User dismisses
            User->>Dialog: Dismiss
            Dialog->>UnifiedManager: Permission denied callback
            UnifiedManager->>GalleryToggle: onToggleDisabled()
            UnifiedManager->>CustomizeToggle: Sync toggle state
            GalleryToggle->>User: Keep toggle disabled
        end
    end

## Proposed File Changes

### ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/overlay/permission/EmojiAccessibilityPermissionTrackerService.kt(NEW)

References: 

- ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/overlay/permission/EmojiOverlayPermissionManager.kt
- ../../app/src/main/java/com/tqhit/battery/one/utils/BatteryLogger.kt

Create a new service specifically for tracking emoji accessibility permission changes as requested by the user. This service will:

- Extend `LifecycleService` to provide lifecycle-aware permission tracking for the emoji module
- Register `AccessibilityManager.AccessibilityStateChangeListener` to monitor emoji accessibility service permission changes
- Expose a `StateFlow<Boolean>` that emits permission status changes specifically for emoji overlay
- Use `EmojiOverlayPermissionManager.isAccessibilityServiceEnabled()` to check current status
- Provide methods to start/stop tracking and handle service lifecycle
- Include comprehensive logging using existing `BatteryLogger` patterns with emoji-specific tags
- Follow the established DI patterns with `@Singleton` annotation for the emoji module
- Emit permission status changes to subscribers when user returns from accessibility settings
- Focus specifically on emoji overlay permissions, not general app permissions

This service will be the single source of truth for emoji accessibility permission status across both emoji screens.

### ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/overlay/permission/EmojiUnifiedToggleManager.kt(NEW)

References: 

- ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/overlay/permission/EmojiOverlayPermissionManager.kt
- ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/overlay/permission/EmojiAccessibilityPermissionTrackerService.kt(NEW)
- ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/overlay/EmojiAccessibilityServiceManager.kt

Create a unified manager specifically for emoji overlay toggle logic used by both EmojiBatteryFragment and EmojiCustomizeActivity. This manager will:

- Provide a single `handleEmojiToggleChange(isEnabled: Boolean, context: Context)` method for both screens
- Use `EmojiOverlayPermissionManager.checkPermissionsAndProceed()` for consistent permission flow
- Implement the user's required flow: check accessibility permission → if true enable toggle and show overlay, if false show accessibility dialog
- Handle dialog outcomes as specified: continue → auto-enable toggle when permission granted, dismiss → keep toggle disabled
- Never emit "permission denied" status as per user requirements - only track enabled/disabled states
- Coordinate with `EmojiAccessibilityPermissionTrackerService` to listen for permission changes
- Provide callbacks for UI updates: `onToggleEnabled`, `onToggleDisabled`, `onPermissionDialogShown`
- Include comprehensive logging with emoji-specific tags and error handling
- Work specifically with emoji overlay functionality and `EmojiAccessibilityServiceManager`
- Expose a `StateFlow<Boolean>` for toggle state that both screens can observe

This ensures both emoji toggles use identical permission checking and user experience flows.

### ../../app/src/main/java/com/tqhit/battery/one/features/emoji/di/EmojiBatteryDIModule.kt(NEW)

References: 

- ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/overlay/permission/EmojiAccessibilityPermissionTrackerService.kt(NEW)
- ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/overlay/permission/EmojiUnifiedToggleManager.kt(NEW)

Update the emoji dependency injection module to provide the new emoji-specific services:

- Add `@Provides @Singleton` method for `EmojiAccessibilityPermissionTrackerService`
- Add `@Provides @Singleton` method for `EmojiUnifiedToggleManager`
- Ensure proper dependency injection setup for the new emoji services
- Include necessary imports for the new emoji-specific classes
- Maintain the existing emoji module DI structure and patterns

This ensures the new emoji services are properly available throughout the emoji module via Hilt dependency injection.

### ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/gallery/EmojiBatteryFragment.kt(NEW)

References: 

- ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/overlay/permission/EmojiUnifiedToggleManager.kt(NEW)
- ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/overlay/permission/EmojiAccessibilityPermissionTrackerService.kt(NEW)

Refactor the emoji gallery toggle to use the unified flow:

- Inject `EmojiUnifiedToggleManager` and `EmojiAccessibilityPermissionTrackerService` using `@Inject` annotations
- Replace the current `globalToggleSwitch.setOnCheckedChangeListener` logic in `setupClickListeners()` to use `EmojiUnifiedToggleManager.handleEmojiToggleChange(isChecked, requireContext())`
- Add lifecycle observation of `EmojiAccessibilityPermissionTrackerService.permissionStatus` in `observeViewModel()` method
- Implement auto-toggle update when permission status changes (user returns from settings with granted permission)
- Remove direct calls to `BatteryGalleryEvent.ToggleGlobalFeature` from the toggle listener
- Add proper toggle state synchronization in `onResume()` using the permission tracker service
- Update `updateGlobalToggle()` method to reflect the actual permission status from the unified manager
- Update logging to use consistent emoji permission tags
- Ensure the toggle reflects the actual accessibility permission status when fragment resumes

This ensures the gallery toggle uses the same flow as the customization toggle and follows the user's requirements.

### ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/EmojiCustomizeActivity.kt(NEW)

References: 

- ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/overlay/permission/EmojiUnifiedToggleManager.kt(NEW)
- ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/overlay/permission/EmojiAccessibilityPermissionTrackerService.kt(NEW)

Refactor the emoji customization toggle to use the unified flow:

- Inject `EmojiUnifiedToggleManager` and `EmojiAccessibilityPermissionTrackerService` using `@Inject` annotations
- Replace the current `globalToggleSwitch.setOnCheckedChangeListener` logic in `setupClickListeners()` to use `EmojiUnifiedToggleManager.handleEmojiToggleChange(isChecked, this)`
- Add lifecycle observation of `EmojiAccessibilityPermissionTrackerService.permissionStatus` in `observeViewModel()` method
- Implement auto-toggle update when permission status changes (user returns from settings with granted permission)
- Remove direct calls to `CustomizeEvent.ToggleGlobalEnabled` from the toggle listener
- Add proper toggle state synchronization in `onResume()` using the permission tracker service
- Update `updateCustomizationControls()` method to reflect the actual permission status from the unified manager
- Update logging to use consistent emoji permission tags
- Ensure the toggle reflects the actual accessibility permission status when activity resumes

This ensures both emoji toggles follow the identical permission flow and user experience.

### ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/gallery/BatteryGalleryViewModel.kt(NEW)

References: 

- ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/overlay/permission/EmojiUnifiedToggleManager.kt(NEW)

Update the gallery ViewModel to integrate with the unified emoji toggle system:

- Inject `EmojiUnifiedToggleManager` in the constructor
- Modify the `handleEvent` method for `BatteryGalleryEvent.ToggleGlobalFeature` to delegate to `EmojiUnifiedToggleManager.handleEmojiToggleChange()`
- Remove direct permission checking logic from the ViewModel since it's now handled by the unified manager
- Update `observeGlobalToggleState()` method to also observe the unified toggle manager's state
- Ensure the ViewModel still emits appropriate effects for UI updates through the existing effect system
- Remove any "permission denied" state emissions as per user requirements
- Add observation of permission tracker service for reactive state updates
- Maintain the existing MVI pattern while delegating toggle logic to the unified manager

This centralizes the emoji permission logic and ensures consistent behavior across the emoji module.

### ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/CustomizeViewModel.kt(NEW)

References: 

- ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/overlay/permission/EmojiUnifiedToggleManager.kt(NEW)

Update the customization ViewModel to use the unified emoji toggle manager:

- Inject `EmojiUnifiedToggleManager` in the constructor
- Modify `toggleGlobalFeature(enabled: Boolean)` method to delegate to `EmojiUnifiedToggleManager.handleEmojiToggleChange(enabled, context)`
- Remove direct permission checking logic from the ViewModel since it's now handled by the unified manager
- Update the method to use callbacks from the unified manager for state updates and effects
- Ensure the ViewModel still emits appropriate effects for UI updates through the existing effect system
- Remove any "permission denied" state emissions as per user requirements
- Add observation of permission tracker service if needed for reactive state updates
- Maintain the existing MVI pattern while delegating toggle logic to the unified manager

This centralizes the emoji permission logic and ensures consistent behavior between both emoji screens.

### ../../app/src/main/AndroidManifest.xml(NEW)

References: 

- ../../app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/overlay/permission/EmojiAccessibilityPermissionTrackerService.kt(NEW)

Register the new EmojiAccessibilityPermissionTrackerService in the manifest:

- Add service declaration for `EmojiAccessibilityPermissionTrackerService` within the emoji feature scope
- Set `android:enabled="true"` and `android:exported="false"`
- Add appropriate service permissions if needed for accessibility monitoring
- Ensure the service can be started by the emoji module components
- Place the service declaration near other emoji-related service declarations

This allows the emoji permission tracking service to run and monitor accessibility permission changes specifically for the emoji overlay feature.