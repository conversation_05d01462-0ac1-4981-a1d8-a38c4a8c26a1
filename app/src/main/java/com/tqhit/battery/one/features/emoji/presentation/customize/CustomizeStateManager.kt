package com.tqhit.battery.one.features.emoji.presentation.customize

import android.content.Context
import android.util.Log
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import javax.inject.Inject
import javax.inject.Singleton

/**
 * State manager for the Customize screen that handles pure state transformations.
 * This class contains all business logic for style selection, customization options,
 * form validation, and preview updates using pure functions.
 * 
 * This follows the reducer pattern and makes state changes predictable and testable.
 * All methods are pure functions with no side effects.
 */
@Singleton
class CustomizeStateManager @Inject constructor() {
    
    companion object {
        private const val TAG = "CustomizeStateMgr"
    }

    /**
     * Helper method to check if user has premium access.
     * This determines whether premium items should be unlocked without reward ads.
     *
     * TODO: Integrate with actual premium subscription system when available.
     * For now, this is a placeholder that can be easily updated.
     */
    private fun hasUserPremiumAccess(): Boolean {
        // TODO: Replace with actual premium subscription check
        // This could check:
        // - In-app purchase status
        // - Subscription status
        // - Premium unlock flags
        // - Server-side premium validation

        Log.d(TAG, "PREMIUM_LOGIC: Checking user premium access (placeholder implementation)")

        // For now, return false to maintain current behavior
        // When premium system is implemented, update this method
        return false
    }
    
    /**
     * Initializes state with a specific battery style
     */
    fun initializeWithStyle(
        currentState: CustomizeUiState,
        style: BatteryStyle
    ): CustomizeUiState {
        Log.d(TAG, "STATE_TRANSFORM: Initializing with style: ${style.name}")
        
        return currentState.copy(
            selectedStyle = style,
            selectedBatteryStyle = style, // For Phase 2 compatibility
            previewConfig = style.defaultConfig,
            showEmojiToggle = style.defaultConfig.showEmoji,
            showPercentageToggle = style.defaultConfig.showPercentage,
            percentageFontSize = style.defaultConfig.percentageFontSizeDp,
            emojiSizeScale = style.defaultConfig.emojiSizeScale,
            percentageColor = style.defaultConfig.percentageColor,
            isInitialLoadComplete = true,
            errorMessage = null
        )
    }
    
    /**
     * Updates state when battery style is selected (Phase 2 mix-and-match)
     */
    fun selectBatteryStyle(
        currentState: CustomizeUiState,
        style: BatteryStyle
    ): CustomizeUiState {
        Log.d(TAG, "STATE_TRANSFORM: Selecting battery style: ${style.name}")
        
        return currentState.copy(
            selectedBatteryStyle = style,
            // Update preview if we have both components
            previewConfig = if (currentState.selectedEmojiStyle != null) {
                createCompositePreviewConfig(style, currentState.selectedEmojiStyle!!, currentState.currentPreviewConfig)
            } else {
                currentState.previewConfig
            }
        )
    }
    
    /**
     * Updates state when emoji style is selected (Phase 2 mix-and-match)
     */
    fun selectEmojiStyle(
        currentState: CustomizeUiState,
        style: BatteryStyle
    ): CustomizeUiState {
        Log.d(TAG, "STATE_TRANSFORM: Selecting emoji style: ${style.name}")
        
        return currentState.copy(
            selectedEmojiStyle = style,
            // Update preview if we have both components
            previewConfig = if (currentState.selectedBatteryStyle != null) {
                createCompositePreviewConfig(currentState.selectedBatteryStyle!!, style, currentState.currentPreviewConfig)
            } else {
                currentState.previewConfig
            }
        )
    }
    
    /**
     * Updates global enabled state
     */
    fun updateGlobalEnabled(
        currentState: CustomizeUiState,
        isEnabled: Boolean
    ): CustomizeUiState {
        Log.d(TAG, "STATE_TRANSFORM: Updating global enabled: $isEnabled")
        
        return currentState.copy(isGlobalEnabled = isEnabled)
    }
    
    /**
     * Updates emoji toggle state
     */
    fun updateShowEmoji(
        currentState: CustomizeUiState,
        showEmoji: Boolean
    ): CustomizeUiState {
        Log.d(TAG, "STATE_TRANSFORM: Updating show emoji: $showEmoji")
        
        val newPreviewConfig = currentState.currentPreviewConfig.copy(showEmoji = showEmoji)
        
        return currentState.copy(
            showEmojiToggle = showEmoji,
            previewConfig = newPreviewConfig
        )
    }
    
    /**
     * Updates percentage toggle state
     */
    fun updateShowPercentage(
        currentState: CustomizeUiState,
        showPercentage: Boolean
    ): CustomizeUiState {
        Log.d(TAG, "STATE_TRANSFORM: Updating show percentage: $showPercentage")
        
        val newPreviewConfig = currentState.currentPreviewConfig.copy(showPercentage = showPercentage)
        
        return currentState.copy(
            showPercentageToggle = showPercentage,
            previewConfig = newPreviewConfig
        )
    }
    
    /**
     * Updates percentage font size
     */
    fun updatePercentageFontSize(
        currentState: CustomizeUiState,
        fontSize: Int
    ): CustomizeUiState {
        Log.d(TAG, "STATE_TRANSFORM: Updating percentage font size: $fontSize")
        
        val clampedSize = fontSize.coerceIn(8, 24) // Reasonable font size range
        val newPreviewConfig = currentState.currentPreviewConfig.copy(percentageFontSizeDp = clampedSize)
        
        return currentState.copy(
            percentageFontSize = clampedSize,
            previewConfig = newPreviewConfig
        )
    }
    
    /**
     * Updates emoji size scale
     */
    fun updateEmojiSizeScale(
        currentState: CustomizeUiState,
        scale: Float
    ): CustomizeUiState {
        Log.d(TAG, "STATE_TRANSFORM: Updating emoji size scale: $scale")
        
        val clampedScale = scale.coerceIn(0.5f, 2.0f) // Reasonable scale range
        val newPreviewConfig = currentState.currentPreviewConfig.copy(emojiSizeScale = clampedScale)
        
        return currentState.copy(
            emojiSizeScale = clampedScale,
            previewConfig = newPreviewConfig
        )
    }
    
    /**
     * Updates percentage color
     */
    fun updatePercentageColor(
        currentState: CustomizeUiState,
        color: Int,
        colorIndex: Int = -1
    ): CustomizeUiState {
        Log.d(TAG, "STATE_TRANSFORM: Updating percentage color: $color")
        
        val newPreviewConfig = currentState.currentPreviewConfig.copy(percentageColor = color)
        
        return currentState.copy(
            percentageColor = color,
            selectedColorIndex = if (colorIndex >= 0) colorIndex else currentState.selectedColorIndex,
            previewConfig = newPreviewConfig
        )
    }
    
    /**
     * Updates color picker visibility
     */
    fun updateColorPickerVisibility(
        currentState: CustomizeUiState,
        isVisible: Boolean
    ): CustomizeUiState {
        Log.d(TAG, "STATE_TRANSFORM: Updating color picker visibility: $isVisible")
        
        return currentState.copy(isColorPickerVisible = isVisible)
    }
    
    /**
     * Updates preview battery level
     */
    fun updatePreviewBatteryLevel(
        currentState: CustomizeUiState,
        level: Int
    ): CustomizeUiState {
        Log.d(TAG, "STATE_TRANSFORM: Updating preview battery level: $level")
        
        val clampedLevel = level.coerceIn(0, 100)
        
        return currentState.copy(previewBatteryLevel = clampedLevel)
    }
    
    /**
     * Updates loading state
     */
    fun updateLoading(
        currentState: CustomizeUiState,
        isLoading: Boolean
    ): CustomizeUiState {
        return currentState.copy(isLoading = isLoading)
    }
    
    /**
     * Updates saving state
     */
    fun updateSaving(
        currentState: CustomizeUiState,
        isSaving: Boolean
    ): CustomizeUiState {
        return currentState.copy(isSaving = isSaving)
    }
    
    /**
     * Updates error state
     */
    fun updateError(
        currentState: CustomizeUiState,
        errorMessage: String?
    ): CustomizeUiState {
        Log.e(TAG, "STATE_TRANSFORM: Setting error state: $errorMessage")
        
        return currentState.copy(
            isLoading = false,
            isSaving = false,
            errorMessage = errorMessage
        )
    }
    
    /**
     * Updates customization config
     */
    fun updateCustomizationConfig(
        currentState: CustomizeUiState,
        config: CustomizationConfig
    ): CustomizeUiState {
        Log.d(TAG, "STATE_TRANSFORM: Updating customization config")
        
        return currentState.copy(
            customizationConfig = config,
            isGlobalEnabled = config.isGlobalEnabled,
            showEmojiToggle = config.customConfig.showEmoji,
            showPercentageToggle = config.customConfig.showPercentage,
            percentageFontSize = config.customConfig.percentageFontSizeDp,
            emojiSizeScale = config.customConfig.emojiSizeScale,
            percentageColor = config.customConfig.percentageColor,
            previewConfig = config.customConfig
        )
    }
    
    /**
     * Resets customization to defaults
     */
    fun resetToDefaults(currentState: CustomizeUiState): CustomizeUiState {
        Log.d(TAG, "STATE_TRANSFORM: Resetting to defaults")
        
        val defaultConfig = BatteryStyleConfig.createDefault()
        
        return currentState.copy(
            showEmojiToggle = defaultConfig.showEmoji,
            showPercentageToggle = defaultConfig.showPercentage,
            percentageFontSize = defaultConfig.percentageFontSizeDp,
            emojiSizeScale = defaultConfig.emojiSizeScale,
            percentageColor = defaultConfig.percentageColor,
            previewConfig = defaultConfig,
            selectedColorIndex = 0, // Reset to white
            isColorPickerVisible = false,
            validationErrors = emptyList(),
            isFormValid = true
        )
    }
    
    /**
     * Validates the current form state
     */
    fun validateForm(currentState: CustomizeUiState): CustomizeUiState {
        Log.d(TAG, "STATE_TRANSFORM: Validating form")

        val errors = mutableListOf<String>()

        // Validate that both battery and emoji styles are selected for Phase 2
        if (currentState.selectedBatteryStyle == null) {
            errors.add("Please select a battery style")
        }

        if (currentState.selectedEmojiStyle == null) {
            errors.add("Please select an emoji style")
        }

        // Validate font size range
        if (currentState.percentageFontSize < 8 || currentState.percentageFontSize > 24) {
            errors.add("Font size must be between 8 and 24")
        }

        // Validate emoji scale range
        if (currentState.emojiSizeScale < 0.5f || currentState.emojiSizeScale > 2.0f) {
            errors.add("Emoji size must be between 0.5x and 2.0x")
        }

        val isValid = errors.isEmpty()

        Log.d(TAG, "STATE_TRANSFORM: Form validation result: isValid=$isValid, errors=${errors.size}")

        return currentState.copy(
            isFormValid = isValid,
            validationErrors = errors
        )
    }

    /**
     * Determines if current selection requires premium access.
     * This method considers both selected styles and user's premium entitlement status.
     */
    fun requiresPremiumAccess(currentState: CustomizeUiState): Boolean {
        val hasPremiumSelection = currentState.hasAnyPremiumSelection
        val userHasPremium = hasUserPremiumAccess()

        // Log comprehensive premium state calculation
        Log.d(TAG, "PREMIUM_LOGIC: Calculating premium access requirements:")
        Log.d(TAG, "PREMIUM_LOGIC:   - Battery style: ${currentState.selectedBatteryStyle?.name} (premium: ${currentState.isSelectedBatteryStylePremium})")
        Log.d(TAG, "PREMIUM_LOGIC:   - Emoji style: ${currentState.selectedEmojiStyle?.name} (premium: ${currentState.isSelectedEmojiStylePremium})")
        Log.d(TAG, "PREMIUM_LOGIC:   - Has any premium selection: $hasPremiumSelection")
        Log.d(TAG, "PREMIUM_LOGIC:   - User has premium access: $userHasPremium")

        // If user has premium access, they can use premium items without ads
        val requiresAd = hasPremiumSelection && !userHasPremium

        Log.d(TAG, "PREMIUM_LOGIC:   - Requires reward ad: $requiresAd")

        return requiresAd
    }

    /**
     * Updates state when premium status changes.
     * Recalculates apply button state and premium requirements.
     */
    fun updatePremiumStatus(currentState: CustomizeUiState): CustomizeUiState {
        Log.d(TAG, "PREMIUM_LOGIC: Updating premium status")

        val requiresAd = requiresPremiumAccess(currentState)
        val canApply = currentState.canApplyChanges // This is a computed property

        Log.d(TAG, "PREMIUM_LOGIC: Premium status update result:")
        Log.d(TAG, "PREMIUM_LOGIC:   - Can apply changes: $canApply")
        Log.d(TAG, "PREMIUM_LOGIC:   - Requires reward ad: $requiresAd")

        // Return the current state as canApplyChanges is computed automatically
        // Note: requiresAd is calculated dynamically via requiresPremiumAccess()
        return currentState
    }

    /**
     * Updates state when battery style is selected with premium validation.
     */
    fun updateWithBatteryStyle(
        currentState: CustomizeUiState,
        style: BatteryStyle
    ): CustomizeUiState {
        Log.d(TAG, "PREMIUM_LOGIC: Updating with battery style: ${style.name} (premium: ${style.isPremium})")

        val newState = selectBatteryStyle(currentState, style)
        return updatePremiumStatus(newState)
    }

    /**
     * Updates state when emoji style is selected with premium validation.
     */
    fun updateWithEmojiStyle(
        currentState: CustomizeUiState,
        style: BatteryStyle
    ): CustomizeUiState {
        Log.d(TAG, "PREMIUM_LOGIC: Updating with emoji style: ${style.name} (premium: ${style.isPremium})")

        val newState = selectEmojiStyle(currentState, style)
        return updatePremiumStatus(newState)
    }
    
    /**
     * Sets loading state
     */
    fun setLoading(
        currentState: CustomizeUiState,
        isLoading: Boolean
    ): CustomizeUiState {
        return currentState.copy(isLoading = isLoading)
    }
    
    /**
     * Initializes state with category data and component lists
     */
    fun initializeWithCategoryData(
        currentState: CustomizeUiState,
        selectedStyle: BatteryStyle,
        availableBatteryStyles: List<BatteryStyle>,
        availableEmojiStyles: List<BatteryStyle>
    ): CustomizeUiState {
        Log.d(TAG, "STATE_TRANSFORM: Initializing with category data")
        Log.d(TAG, "STATE_TRANSFORM: Selected style: ${selectedStyle.name}")
        Log.d(TAG, "STATE_TRANSFORM: Available battery styles: ${availableBatteryStyles.size}")
        Log.d(TAG, "STATE_TRANSFORM: Available emoji styles: ${availableEmojiStyles.size}")
        
        // Set initial selection to first items (which should be the originally selected style)
        val selectedBatteryStyle = availableBatteryStyles.firstOrNull() ?: selectedStyle
        val selectedEmojiStyle = availableEmojiStyles.firstOrNull() ?: selectedStyle
        
        Log.d(TAG, "STATE_TRANSFORM: Initial battery selection: ${selectedBatteryStyle.name}")
        Log.d(TAG, "STATE_TRANSFORM: Initial emoji selection: ${selectedEmojiStyle.name}")
        
        return currentState.copy(
            isLoading = false,
            selectedStyle = selectedStyle,
            selectedBatteryStyle = selectedBatteryStyle,
            selectedEmojiStyle = selectedEmojiStyle,
            availableBatteryStyles = availableBatteryStyles,
            availableEmojiStyles = availableEmojiStyles,
            previewConfig = selectedStyle.defaultConfig,
            showEmojiToggle = selectedStyle.defaultConfig.showEmoji,
            showPercentageToggle = selectedStyle.defaultConfig.showPercentage,
            percentageFontSize = selectedStyle.defaultConfig.percentageFontSizeDp,
            emojiSizeScale = selectedStyle.defaultConfig.emojiSizeScale,
            percentageColor = selectedStyle.defaultConfig.percentageColor,
            isInitialLoadComplete = true,
            errorMessage = null
        )
    }
    
    // Private helper methods
    
    /**
     * Creates a composite preview config from battery and emoji styles
     */
    private fun createCompositePreviewConfig(
        batteryStyle: BatteryStyle,
        emojiStyle: BatteryStyle,
        currentConfig: BatteryStyleConfig
    ): BatteryStyleConfig {
        Log.d(TAG, "STATE_TRANSFORM: Creating composite preview config")
        
        // Merge configurations, preserving user customizations
        return currentConfig.copy(
            // Could add logic here to merge specific properties from both styles
            // For now, keep the current user customizations
        )
    }
}
