I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end. Make sure you fix all the linting, compilation or validation issues after successful implementation of the plan.

### Observations

After exploring the emoji module codebase, I found that the current architecture already has most components needed for the requested functionality. The CustomizeUiState already contains `availableBatteryStyles` and `availableEmojiStyles` lists, and the adapters (BatteryComponentAdapter and EmojiComponentAdapter) are designed to display lists of components with selection states. However, the current implementation only loads a single BatteryStyle and doesn't populate these lists from the category data.

The key insight is that the adapters already work with lists of BatteryStyle objects and extract either `batteryImageUrl` or `emojiImageUrl` respectively. The selection mechanism is also in place with `updateSelection()` methods. The main work involves modifying the data flow to load all items from a category and populate these lists correctly.

### Approach

The implementation will extend the existing MVI architecture to support category-based loading while maintaining backward compatibility. The approach involves:

1. **Navigation Enhancement**: Modify the navigation from Gallery to Customize to pass both the selected item and its category information
2. **Data Loading Extension**: Add new events and state management to load all items from a category via EmojiItemService
3. **List Population**: Populate the existing `availableBatteryStyles` and `availableEmojiStyles` lists in CustomizeUiState
4. **Selection Logic**: Implement logic to reorder lists so the originally tapped item appears first and is pre-selected
5. **UI Updates**: Ensure the existing adapters receive the populated lists and selection states

This approach leverages the existing infrastructure while adding the new category-based functionality.

### Reasoning

I explored the emoji module codebase by reading the summary document and examining key files. I analyzed the current navigation flow from EmojiBatteryFragment to EmojiCustomizeActivity, studied the data models (BatteryStyle, EmojiItem), examined the data services (EmojiItemService, EmojiCategoryService), and reviewed the customize screen architecture including CustomizeUiState, CustomizeEvent, and the component adapters. This exploration revealed that the infrastructure for displaying component lists already exists but needs to be connected to category-based data loading.

## Mermaid Diagram

sequenceDiagram
    participant User
    participant EmojiBatteryFragment
    participant EmojiCustomizeActivity
    participant CustomizeViewModel
    participant EmojiItemService
    participant BatteryComponentAdapter
    participant EmojiComponentAdapter

    User->>EmojiBatteryFragment: Tap on gallery item
    EmojiBatteryFragment->>EmojiBatteryFragment: Extract category ID from selected style
    EmojiBatteryFragment->>EmojiCustomizeActivity: createIntent(context, style, categoryId)
    EmojiCustomizeActivity->>CustomizeViewModel: InitializeWithCategoryAndStyle(categoryId, selectedStyle)
    CustomizeViewModel->>EmojiItemService: getEmojiItemsByCategory(categoryId)
    EmojiItemService-->>CustomizeViewModel: List<EmojiItem>
    CustomizeViewModel->>CustomizeViewModel: Convert to BatteryStyle objects
    CustomizeViewModel->>CustomizeViewModel: Reorder lists (selected item first)
    CustomizeViewModel->>CustomizeViewModel: Update state with component lists
    CustomizeViewModel-->>EmojiCustomizeActivity: Updated CustomizeUiState
    EmojiCustomizeActivity->>BatteryComponentAdapter: submitList(availableBatteryStyles)
    EmojiCustomizeActivity->>EmojiComponentAdapter: submitList(availableEmojiStyles)
    EmojiCustomizeActivity->>BatteryComponentAdapter: updateSelection(selectedBatteryStyle.id)
    EmojiCustomizeActivity->>EmojiComponentAdapter: updateSelection(selectedEmojiStyle.id)
    BatteryComponentAdapter-->>User: Display battery components (selected item first)
    EmojiComponentAdapter-->>User: Display emoji components (selected item first)

## Proposed File Changes

### app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/CustomizeEvent.kt(MODIFY)

Add a new event `InitializeWithCategoryAndStyle` that accepts both a category ID and the originally selected BatteryStyle. This event will trigger loading all items from the category while maintaining reference to the originally tapped item. The event should include parameters for `categoryId: String` and `selectedStyle: BatteryStyle` to support the new category-based initialization flow.

### app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/EmojiCustomizeActivity.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/CustomizeEvent.kt(MODIFY)
- app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/adapter/BatteryComponentAdapter.kt
- app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/adapter/EmojiComponentAdapter.kt
- app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/CustomizeUiState.kt

Update the `createIntent` companion method to accept an additional `categoryId` parameter alongside the existing `style` parameter. Modify the intent creation to pass both the selected style and category ID as extras. Update the `onCreate` method to extract both the style and category ID from the intent extras. When both are present, trigger the new `InitializeWithCategoryAndStyle` event instead of the existing `InitializeWithStyle` event. Maintain backward compatibility by falling back to the existing behavior when only style is provided.
Update the UI update methods to properly handle the populated component lists. Modify `updateStyleSelections` method to submit the `availableBatteryStyles` list to the `BatteryComponentAdapter` and the `availableEmojiStyles` list to the `EmojiComponentAdapter` using their existing `submitList()` methods. Update the selection states by calling `updateSelection()` on both adapters with the IDs of the currently selected battery and emoji styles. Ensure the adapters are properly initialized and the RecyclerViews are set up to display the horizontal lists of components. Add logging to track the successful population of component lists and selection updates.

### app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/gallery/EmojiBatteryFragment.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/EmojiCustomizeActivity.kt(MODIFY)
- app/src/main/java/com/tqhit/battery/one/features/emoji/domain/model/BatteryStyle.kt

Modify the `navigateToCustomization` method to pass the category information along with the selected style. Extract the category ID from the selected BatteryStyle (using `style.category` to get the BatteryStyleCategory, then map it to the corresponding category ID string). Update the intent creation call to use the enhanced `EmojiCustomizeActivity.createIntent` method that accepts both style and category ID parameters. This ensures the customize screen receives both the selected item and its category context.

### app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/CustomizeViewModel.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/features/emoji/data/service/EmojiItemService.kt
- app/src/main/java/com/tqhit/battery/one/features/emoji/domain/model/BatteryStyle.kt
- app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/CustomizeUiState.kt

Add dependency injection for `EmojiItemService` in the constructor to enable loading category data. Implement handling for the new `InitializeWithCategoryAndStyle` event in the event processing logic. When this event is received, use `EmojiItemService.getEmojiItemsByCategory()` to load all items from the specified category. Convert the loaded EmojiItems to BatteryStyle objects using the existing `toBatteryStyle()` method. Create separate lists for battery components and emoji components from the loaded styles. Implement logic to reorder both lists so the originally selected item appears at index 0. Update the state with the populated `availableBatteryStyles` and `availableEmojiStyles` lists, and set the initial selections to the first item (index 0) in each list.

### app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/CustomizeStateManager.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/CustomizeUiState.kt
- app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/CustomizeEvent.kt(MODIFY)

Add state management logic for the new `InitializeWithCategoryAndStyle` event. When this event is processed, set the loading state to true initially. Add logic to handle the successful loading of category items by updating the `availableBatteryStyles` and `availableEmojiStyles` lists in the state. Implement the reordering logic to ensure the originally selected item appears first in both lists. Set the `selectedBatteryStyle` and `selectedEmojiStyle` to the first items in their respective lists (which will be the originally tapped item). Update `isInitialLoadComplete` to true and clear any loading states once the category data is successfully loaded and processed.

### app/src/main/java/com/tqhit/battery/one/features/emoji/domain/model/BatteryStyleCategory.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/features/emoji/domain/model/EmojiCategory.kt

Add a method `toCategoryId()` that maps each BatteryStyleCategory enum value to its corresponding remote config category ID string. This should return the appropriate category ID (e.g., HOT -> "hot_category", CHARACTER -> "character_category", etc.) to enable reverse mapping from the enum to the category ID needed for data loading. This method will be used in the navigation flow to determine which category data to load in the customize screen.