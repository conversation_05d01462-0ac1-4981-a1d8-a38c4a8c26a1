package com.tqhit.battery.one.features.emoji.presentation.customize

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle

/**
 * Events for the emoji battery customization screen following MVI pattern.
 * These events represent user interactions and system events that trigger state changes.
 *
 * This event class provides backward compatibility with the existing Fragment/Activity
 * implementations while working with the new MVI architecture.
 */
sealed class CustomizeEvent {

    // Initialization events
    object LoadInitialData : CustomizeEvent()
    data class InitializeWithStyle(val style: BatteryStyle) : CustomizeEvent()
    data class InitializeWithCategoryAndStyle(val categoryId: String, val selectedStyle: BatteryStyle) : CustomizeEvent()

    // Style selection events
    data class SelectBatteryStyle(val style: BatteryStyle) : CustomizeEvent()
    data class SelectEmojiStyle(val style: BatteryStyle) : CustomizeEvent()

    // Toggle events
    data class ToggleGlobalEnabled(val enabled: Boolean) : CustomizeEvent()

    // Permission events
    data class HandlePermissionResult(val granted: Boolean) : CustomizeEvent()

    // UI interaction events
    data class ToggleShowEmoji(val show: Boolean) : CustomizeEvent()
    data class ToggleShowPercentage(val show: Boolean) : CustomizeEvent()
    data class UpdatePercentageFontSize(val size: Int) : CustomizeEvent()
    data class UpdateEmojiSizeScale(val scale: Float) : CustomizeEvent()
    data class UpdatePercentageColor(val color: Int) : CustomizeEvent()
    data class UpdatePreviewBatteryLevel(val level: Int) : CustomizeEvent()

    // Color picker events
    object ShowColorPicker : CustomizeEvent()
    object HideColorPicker : CustomizeEvent()
    data class SelectColor(val color: Int, val index: Int) : CustomizeEvent()

    // Preview events
    object RefreshPreview : CustomizeEvent()

    // Apply and save events
    object ApplyCustomization : CustomizeEvent()
    object ApplyPremiumCustomization : CustomizeEvent()
    object ResetToDefaults : CustomizeEvent()

    // Navigation events
    object NavigateBack : CustomizeEvent()
    object ClearNavigationState : CustomizeEvent()

    // Lifecycle events
    object OnResume : CustomizeEvent()
    object OnPause : CustomizeEvent()

    // Error handling events
    object RetryLoad : CustomizeEvent()
    object ClearError : CustomizeEvent()
    object ValidateForm : CustomizeEvent()
    data class ShowValidationError(val message: String) : CustomizeEvent()
}
