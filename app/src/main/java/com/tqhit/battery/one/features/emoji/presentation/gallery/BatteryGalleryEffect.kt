package com.tqhit.battery.one.features.emoji.presentation.gallery

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle

/**
 * Sealed class representing one-time side effects for the Battery Gallery screen.
 * These effects are separate from UI state to prevent re-triggering issues when state is recreated.
 * 
 * Effects are consumed once by the UI and should not be stored in persistent state.
 * This follows modern MVI pattern where side effects are handled separately from UI state.
 */
sealed class BatteryGalleryEffect {
    
    /**
     * Navigation effects for moving between screens
     */
    sealed class Navigation : BatteryGalleryEffect() {
        /**
         * Navigate to the customization screen with the selected battery style
         */
        data class NavigateToCustomize(val style: BatteryStyle) : Navigation()
    }
    
    /**
     * Dialog effects for showing various dialogs to the user
     */
    sealed class Dialog : BatteryGalleryEffect() {
        /**
         * Show premium unlock dialog for a premium battery style
         */
        data class ShowPremiumDialog(val style: BatteryStyle) : Dialog()
        
        /**
         * Show permission request dialog for accessibility permissions
         */
        object ShowPermissionDialog : Dialog()
        
        /**
         * Show information dialog about the emoji battery feature
         */
        object ShowInfoDialog : Dialog()
        
        /**
         * Show error dialog with specific error message and retry option
         */
        data class ShowErrorDialog(
            val message: String,
            val isRetryable: Boolean = true
        ) : Dialog()
    }
    
    /**
     * User feedback effects for showing messages and notifications
     */
    sealed class UserFeedback : BatteryGalleryEffect() {
        /**
         * Show a toast message to the user
         */
        data class ShowToast(val message: String) : UserFeedback()
        
        /**
         * Show a snackbar message with optional action
         */
        data class ShowSnackbar(
            val message: String,
            val actionText: String? = null,
            val action: (() -> Unit)? = null
        ) : UserFeedback()
        
        /**
         * Show success message when an operation completes successfully
         */
        data class ShowSuccessMessage(val message: String) : UserFeedback()
        
        /**
         * Show error message when an operation fails
         */
        data class ShowErrorMessage(val message: String) : UserFeedback()
    }
    
    /**
     * System interaction effects for permissions and external actions
     */
    sealed class SystemInteraction : BatteryGalleryEffect() {
        /**
         * Request accessibility permissions from the system
         */
        object RequestAccessibilityPermissions : SystemInteraction()
        
        /**
         * Open system settings for accessibility permissions
         */
        object OpenAccessibilitySettings : SystemInteraction()
        
        /**
         * Request overlay permissions from the system
         */
        object RequestOverlayPermissions : SystemInteraction()
        
        /**
         * Open system settings for overlay permissions
         */
        object OpenOverlaySettings : SystemInteraction()
        
        /**
         * Trigger a reward ad for premium content unlock
         */
        data class ShowRewardAd(val style: BatteryStyle) : SystemInteraction()
        
        /**
         * Open the premium purchase flow
         */
        object OpenPremiumPurchase : SystemInteraction()
    }
    
    /**
     * Data effects for triggering data operations
     */
    sealed class DataOperation : BatteryGalleryEffect() {
        /**
         * Refresh data from remote config
         */
        object RefreshData : DataOperation()
        
        /**
         * Clear cache and reload data
         */
        object ClearCacheAndReload : DataOperation()
        
        /**
         * Load more items (for pagination if implemented)
         */
        object LoadMoreItems : DataOperation()
    }
    
    /**
     * Analytics effects for tracking user interactions
     */
    sealed class Analytics : BatteryGalleryEffect() {
        /**
         * Track category selection event
         */
        data class TrackCategorySelected(val categoryName: String) : Analytics()
        
        /**
         * Track style selection event
         */
        data class TrackStyleSelected(val styleName: String, val isPremium: Boolean) : Analytics()
        
        /**
         * Track search event
         */
        data class TrackSearchPerformed(val query: String, val resultsCount: Int) : Analytics()
        
        /**
         * Track premium unlock attempt
         */
        data class TrackPremiumUnlockAttempt(val styleName: String, val method: String) : Analytics()
        
        /**
         * Track error occurrence
         */
        data class TrackError(val errorType: String, val errorMessage: String) : Analytics()
    }
}
