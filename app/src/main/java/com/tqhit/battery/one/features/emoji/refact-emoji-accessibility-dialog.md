I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end. Make sure you fix all the linting, compilation or validation issues after successful implementation of the plan.

### Observations

I've thoroughly explored the codebase and understand the current emoji module architecture. The app uses Clean Architecture with MVI pattern, Hilt DI, and has an established dialog system using `AdLibBaseDialog`. The current permission flow uses `EmojiOverlayPermissionManager` with basic `NotificationDialog`. The app has comprehensive theming with multiple variants (Light, Grey, Amoled, Black + Inverted versions) and uses custom color attributes. The trigger points are in `EmojiBatteryFragment` and `EmojiCustomizeActivity` where users enable emoji battery functionality.

### Approach

Replace the current basic accessibility permission dialog with a comprehensive two-step flow: an **Accessibility Rationale Dialog** with checkbox agreement and a **Guide Activity** with visual step-by-step instructions. This enhances user trust and increases permission grant success rates by clearly explaining the purpose and providing visual guidance through Android's accessibility settings. The implementation will follow existing app patterns for dialogs, activities, theming, and permission management while integrating seamlessly with the current emoji module flow.

### Reasoning

I explored the codebase structure and found the emoji module uses Clean Architecture with MVI pattern. I examined the current permission flow in `EmojiOverlayPermissionManager` which uses basic `NotificationDialog`. I analyzed existing dialog patterns in `SelectLanguageDialog` and `SelectThemeDialog` to understand the app's UI conventions. I reviewed the theme system with multiple variants and custom color attributes. I identified trigger points in `EmojiBatteryFragment` and `EmojiCustomizeActivity` where permission requests occur. I examined string resources to understand localization patterns and found the app uses `AdLibBaseDialog` as the base for custom dialogs.

## Mermaid Diagram

sequenceDiagram
    participant User
    participant Fragment as EmojiBatteryFragment/EmojiCustomizeActivity
    participant PermissionManager as EmojiOverlayPermissionManager
    participant RationaleDialog as AccessibilityRationaleDialog
    participant GuideActivity as AccessibilityGuideActivity
    participant AndroidSettings as Android Settings

    User->>Fragment: Taps "Enable Emoji Battery"
    Fragment->>PermissionManager: checkPermissionsAndProceed()
    PermissionManager->>PermissionManager: Check accessibility service status
    
    alt Accessibility Service Not Enabled
        PermissionManager->>RationaleDialog: Show rationale dialog
        RationaleDialog->>User: Display permission explanation + checkbox
        User->>RationaleDialog: Checks "I Agree" and taps "Next"
        RationaleDialog->>GuideActivity: Launch guide activity
        GuideActivity->>User: Show step-by-step visual guide
        User->>GuideActivity: Taps "Go to Setting"
        GuideActivity->>AndroidSettings: Open accessibility settings
        User->>AndroidSettings: Enables Emoji Battery service
        User->>GuideActivity: Returns to guide activity
        GuideActivity->>GuideActivity: onResume() - check permission status
        
        alt Permission Granted
            GuideActivity->>Fragment: Finish activity & return
            Fragment->>PermissionManager: Permission granted callback
            PermissionManager->>Fragment: onAllPermissionsGranted()
        else Permission Still Denied
            GuideActivity->>User: Remain on guide (can retry)
        end
    else Accessibility Service Already Enabled
        PermissionManager->>Fragment: onAllPermissionsGranted()
    end

## Proposed File Changes

### app/src/main/res/layout/dialog_accessibility_rationale.xml(NEW)

References: 

- app/src/main/res/layout/dialog_notification.xml
- app/src/main/res/values/themes.xml

Create a new layout file for the Accessibility Rationale Dialog following the PRD specifications and app's existing dialog patterns. The layout should include:

- Root container with semi-transparent scrim background
- Main dialog container with 16dp corner radius using `@drawable/white_block` background
- Title TextView with "Accessibility Service Usage" text, bold 20sp font, using `?attr/colorr` for primary brand color, center aligned
- Body content LinearLayout with the specified text about Terms of Service and accessibility permissions, using 16sp regular font and `?attr/black` color
- Checkbox with "I Agree" label using app's existing checkbox styling
- Button container with "Close" text button (using `?attr/unselected_button` color) and "Next" contained button with disabled/enabled states as specified in PRD
- Follow the same structure and styling patterns as `dialog_notification.xml` but adapted for the new content and checkbox requirement
- Use theme-aware colors and existing drawable resources for consistency

### app/src/main/res/layout/activity_accessibility_guide.xml(NEW)

References: 

- app/src/main/res/layout/activity_main.xml
- app/src/main/res/values/themes.xml

Create a new layout file for the AccessibilityGuideActivity following the PRD specifications and app's existing activity patterns. The layout should include:

- Root container with theme-aware background color using `?attr/grey` attribute
- Toolbar/ActionBar area with back navigation button using existing `@drawable/ic_back` icon
- ScrollView container to ensure content is viewable on all screen sizes
- Main content LinearLayout with vertical orientation
- Three instructional steps, each containing:
  - Step title TextView with numbered instructions ("1. Select 'Installed Apps'...", etc.) using white text color for dark background
  - ImageView for step screenshots with appropriate sizing and margins
- Bottom "Go to Setting" button anchored to bottom with full-width layout, using gradient background (create new drawable with linear gradient from `#34C7F8` to `#2A80F5`), white text, and proper margins
- Use existing dimension resources and follow the app's spacing conventions
- Ensure proper theme adaptation for different theme variants

### app/src/main/java/com/tqhit/battery/one/dialog/accessibility/AccessibilityRationaleDialog.kt(NEW)

References: 

- app/src/main/java/com/tqhit/battery/one/dialog/language/SelectLanguageDialog.kt
- app/src/main/java/com/tqhit/battery/one/dialog/utils/NotificationDialog.kt

Create a new dialog class extending `AdLibBaseDialog` following the existing dialog patterns from `SelectLanguageDialog` and `NotificationDialog`. The dialog should:

- Extend `AdLibBaseDialog<DialogAccessibilityRationaleBinding>` with proper ViewBinding setup
- Include constructor parameters for context and callback functions (onNext, onClose)
- Override `initWindow()` to set transparent background, proper layout params, and disable outside touch cancellation
- Override `setupUI()` to initialize the dialog content, set title and body text from string resources
- Override `setupListener()` to handle checkbox state changes and button clicks
- Implement checkbox listener to enable/disable the "Next" button based on agreement state
- Handle "Close" button click to dismiss dialog and call onClose callback
- Handle "Next" button click to dismiss dialog and call onNext callback (only when checkbox is checked)
- Apply proper button styling for disabled/enabled states as specified in PRD
- Include comprehensive logging using `BatteryLogger` following app's logging patterns
- Follow the same error handling patterns as existing dialogs

### app/src/main/java/com/tqhit/battery/one/activity/accessibility/AccessibilityGuideActivity.kt(NEW)

References: 

- app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/EmojiCustomizeActivity.kt
- app/src/main/java/com/tqhit/battery/one/base/LocaleAwareActivity.kt

Create a new activity class extending `LocaleAwareActivity` following the existing activity patterns from `EmojiCustomizeActivity`. The activity should:

- Extend `LocaleAwareActivity` with ViewBinding support using `ActivityAccessibilityGuideBinding`
- Include proper Hilt `@AndroidEntryPoint` annotation for dependency injection
- Override `onCreate()` to set up the activity, apply current theme, initialize UI components
- Set up action bar with back navigation using existing patterns from `EmojiCustomizeActivity`
- Load step instruction images from drawable resources (step1.png, step2.png, step3.png)
- Handle "Go to Setting" button click to open `Settings.ACTION_ACCESSIBILITY_SETTINGS` intent
- Override `onResume()` to check accessibility permission status and finish activity if permission is granted
- Include proper back navigation handling and activity lifecycle management
- Apply theme-aware styling and colors using existing theme attributes
- Implement comprehensive logging using `BatteryLogger` with appropriate tags
- Handle potential errors when opening settings (fallback to general settings)
- Follow the same error handling and navigation patterns as existing activities

### app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/overlay/permission/EmojiOverlayPermissionManager.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/dialog/accessibility/AccessibilityRationaleDialog.kt(NEW)
- app/src/main/java/com/tqhit/battery/one/activity/accessibility/AccessibilityGuideActivity.kt(NEW)

Update the `EmojiOverlayPermissionManager` to integrate the new two-step accessibility permission flow. The modifications should:

- Add a new public method `showAccessibilityPermissionFlow()` that replaces the current basic dialog approach
- Modify the existing `requestAccessibilityPermission()` method to use the new `AccessibilityRationaleDialog` instead of `NotificationDialog`
- Implement the flow logic: show rationale dialog first, then navigate to guide activity when user clicks "Next"
- Add callback handling to manage the complete flow from rationale dialog through guide activity completion
- Update the `checkPermissionsAndProceed()` method to use the new flow when accessibility permission is needed
- Maintain backward compatibility with existing callers by keeping the same public API surface
- Add proper error handling and logging for the new flow components
- Include logic to handle activity result or permission state changes after returning from settings
- Follow the existing code patterns and logging conventions used throughout the file
- Ensure the new flow integrates seamlessly with the existing overlay permission handling for older Android versions

### app/src/main/res/values/strings.xml(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/features/emoji/refact-emoji-accessibility-dialog.md

Add new string resources for the enhanced accessibility dialog flow following the existing string resource patterns and naming conventions. Add the following strings:

- `accessibility_service_usage_title` for the dialog title
- `accessibility_service_usage_message` for the dialog body text with the exact content specified in the PRD
- `i_agree` for the checkbox label
- `accessibility_guide_title` for the guide activity title
- `accessibility_guide_step1`, `accessibility_guide_step2`, `accessibility_guide_step3` for the step instructions
- `go_to_setting` for the guide activity button
- `accessibility_rationale_close` and `accessibility_rationale_next` for dialog buttons
- Follow the existing string naming conventions and organization
- Ensure strings are properly formatted and escaped for XML
- Add appropriate comments to group the new accessibility-related strings
- Consider the existing localization structure for future translation support

### app/src/main/res/drawable/gradient_button_background.xml(NEW)

References: 

- app/src/main/res/drawable/grey_block.xml

Create a new drawable resource for the gradient button background used in the AccessibilityGuideActivity. The drawable should:

- Define a gradient drawable with linear gradient from `#34C7F8` to `#2A80F5` as specified in the PRD
- Include proper corner radius (8dp) to match existing button styling in the app
- Use `<gradient>` element with `android:startColor` and `android:endColor` attributes
- Set appropriate `android:angle` for the gradient direction (typically 0 for left-to-right or 90 for top-to-bottom)
- Follow the existing drawable resource patterns and naming conventions
- Ensure the gradient provides good contrast with white text
- Consider adding a subtle pressed state or selector if needed for better user feedback

### app/src/main/res/drawable-xxhdpi/accessibility_step1.png(NEW)

References: 

- app/src/main/res/drawable-xxhdpi/accessibility_step1.png(NEW)

Add the first step screenshot image for the accessibility guide showing the main Accessibility settings page with "Installed Apps" or "Downloaded App" highlighted. This should be a high-quality screenshot from a recent Android version (12/13) showing the accessibility settings menu. The image should be optimized for xxhdpi density and follow the app's existing image resource conventions. The screenshot should clearly show the path users need to follow to find the installed apps section in accessibility settings.

### app/src/main/res/drawable-xxhdpi/accessibility_step2.png(NEW)

References: 

- app/src/main/res/drawable-xxhdpi/accessibility_step2.png(NEW)

Add the second step screenshot image showing the "Emoji Battery" item in the accessibility services list. This should be a high-quality screenshot showing how the app appears in the installed accessibility services list. The image should be optimized for xxhdpi density and clearly highlight where users should tap to select the Emoji Battery service.

### app/src/main/res/drawable-xxhdpi/accessibility_step3.png(NEW)

References: 

- app/src/main/res/drawable-xxhdpi/accessibility_step3.png(NEW)

Add the third step screenshot image showing the final system confirmation dialog ("Allow Emoji Battery to have full control...") with the toggle switch to enable the service. This should be a high-quality screenshot showing the final permission grant screen that users will see. The image should be optimized for xxhdpi density and clearly show the toggle or button users need to activate.

### app/src/main/AndroidManifest.xml(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/EmojiCustomizeActivity.kt

Add the new `AccessibilityGuideActivity` to the Android manifest following the existing activity declaration patterns. The activity declaration should:

- Include the full class name `com.tqhit.battery.one.activity.accessibility.AccessibilityGuideActivity`
- Set appropriate theme using `android:theme="@style/Theme.BatteryOne.NoActionBar"` to match other activities
- Include `android:exported="false"` for security since it's only used internally
- Add `android:screenOrientation="portrait"` if the app enforces portrait orientation
- Include `android:launchMode="singleTop"` to prevent multiple instances
- Follow the same pattern and attributes used by other activities like `EmojiCustomizeActivity`
- Ensure proper indentation and formatting consistent with the existing manifest structure