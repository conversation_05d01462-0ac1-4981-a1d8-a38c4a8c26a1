package com.tqhit.battery.one.features.emoji.presentation.overlay

import android.content.Context
import android.content.Intent
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import com.tqhit.battery.one.features.emoji.presentation.overlay.permission.EmojiOverlayPermissionManager
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Helper class for managing the emoji battery accessibility service lifecycle.
 * Provides centralized control for starting, stopping, and monitoring the accessibility service.
 * 
 * This helper follows the established patterns in the app:
 * - Uses Hilt for dependency injection
 * - Integrates with existing repository pattern
 * - Uses BatteryLogger for comprehensive logging
 * - Follows service management patterns from other features
 * - Supports reactive configuration updates
 * - Avoids conflicts with ChargingOverlayServiceHelper
 */
@Singleton
class EmojiAccessibilityServiceManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val customizationRepository: CustomizationRepository
) {
    
    companion object {
        private const val TAG = "EmojiAccessibilityServiceManager"
        private const val EMOJI_OVERLAY_TAG = "EmojiOverlay_Lifecycle"
        private const val EMOJI_CONFIG_TAG = "EmojiOverlay_Config"
        private const val EMOJI_PERMISSION_TAG = "EmojiOverlay_Permission"
    }
    
    // Coroutine scope for background operations
    private val managerScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    // Service state tracking
    private var isMonitoringEmojiConfig = false
    
    init {
        BatteryLogger.d(TAG, "EmojiAccessibilityServiceManager initialized")
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "Starting emoji overlay service manager initialization")
        startEmojiConfigurationMonitoring()
    }
    
    /**
     * Starts monitoring emoji configuration changes to automatically manage accessibility service state.
     */
    private fun startEmojiConfigurationMonitoring() {
        if (isMonitoringEmojiConfig) {
            BatteryLogger.d(EMOJI_CONFIG_TAG, "Emoji configuration monitoring already started")
            return
        }
        
        BatteryLogger.d(EMOJI_CONFIG_TAG, "Starting emoji configuration monitoring")
        managerScope.launch {
            customizationRepository.getCustomizationConfigFlow()
                .catch { exception ->
                    BatteryLogger.e(TAG, "Error monitoring emoji configuration changes", exception)
                }
                .collect { config ->
                    BatteryLogger.d(EMOJI_CONFIG_TAG, "Emoji configuration changed - global enabled: ${config.isGlobalEnabled}")
                    handleEmojiConfigurationChange(config.isGlobalEnabled)
                }
        }
        
        isMonitoringEmojiConfig = true
        BatteryLogger.d(EMOJI_CONFIG_TAG, "Emoji configuration monitoring started successfully")
    }
    
    /**
     * Handles emoji configuration changes and manages accessibility service state accordingly.
     */
    private fun handleEmojiConfigurationChange(isEnabled: Boolean) {
        BatteryLogger.d(EMOJI_CONFIG_TAG, "Handling emoji configuration change - enabled: $isEnabled")
        
        if (isEnabled) {
            BatteryLogger.d(EMOJI_PERMISSION_TAG, "Emoji overlay enabled, checking permissions")
            if (EmojiOverlayPermissionManager.hasAllRequiredPermissions(context)) {
                BatteryLogger.d(EMOJI_PERMISSION_TAG, "All permissions granted, starting emoji accessibility service")
                startEmojiAccessibilityService()
            } else {
                BatteryLogger.w(EMOJI_PERMISSION_TAG, "Cannot start emoji accessibility service - missing required permissions")
                val permissionStatus = EmojiOverlayPermissionManager.getPermissionStatusSummary(context)
                BatteryLogger.w(EMOJI_PERMISSION_TAG, "Permission status: $permissionStatus")
            }
        } else {
            BatteryLogger.d(EMOJI_CONFIG_TAG, "Emoji overlay disabled, stopping accessibility service")
            stopEmojiAccessibilityService()
        }
    }
    
    /**
     * Starts the emoji battery accessibility service.
     */
    fun startEmojiAccessibilityService() {
        BatteryLogger.d(TAG, "Starting emoji battery accessibility service")
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_SERVICE_START_REQUESTED")
        
        try {
            if (!EmojiOverlayPermissionManager.hasAllRequiredPermissions(context)) {
                BatteryLogger.w(EMOJI_PERMISSION_TAG, "Cannot start emoji accessibility service - missing required permissions")
                val permissionStatus = EmojiOverlayPermissionManager.getPermissionStatusSummary(context)
                BatteryLogger.w(EMOJI_PERMISSION_TAG, "Detailed permission status: $permissionStatus")
                return
            }
            
            if (isEmojiAccessibilityServiceRunning()) {
                BatteryLogger.d(EMOJI_OVERLAY_TAG, "Emoji accessibility service already running, skipping start")
                return
            }
            
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "Sending intent to show emoji overlay")
            // The accessibility service is started by the system when enabled
            // We just need to send an intent to show the overlay
            val intent = Intent(context, EmojiBatteryAccessibilityService::class.java).apply {
                action = EmojiBatteryAccessibilityService.ACTION_SHOW_OVERLAY
            }
            
            context.startService(intent)
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_SERVICE_START_INTENT_SENT")
            
        } catch (exception: Exception) {
            BatteryLogger.e(TAG, "Error starting emoji accessibility service", exception)
            BatteryLogger.e(EMOJI_OVERLAY_TAG, "EMOJI_SERVICE_START_FAILED: ${exception.message}")
        }
    }
    
    /**
     * Stops the emoji battery accessibility service.
     */
    fun stopEmojiAccessibilityService() {
        BatteryLogger.d(TAG, "Stopping emoji battery accessibility service")
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_SERVICE_STOP_REQUESTED")
        
        try {
            if (!isEmojiAccessibilityServiceRunning()) {
                BatteryLogger.d(EMOJI_OVERLAY_TAG, "Emoji accessibility service not running, skipping stop")
                return
            }
            
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "Sending intent to hide emoji overlay")
            // Send intent to hide the overlay
            val intent = Intent(context, EmojiBatteryAccessibilityService::class.java).apply {
                action = EmojiBatteryAccessibilityService.ACTION_HIDE_OVERLAY
            }
            
            context.startService(intent)
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_SERVICE_STOP_INTENT_SENT")
            
        } catch (exception: Exception) {
            BatteryLogger.e(TAG, "Error stopping emoji accessibility service", exception)
            BatteryLogger.e(EMOJI_OVERLAY_TAG, "EMOJI_SERVICE_STOP_FAILED: ${exception.message}")
        }
    }
    
    /**
     * Updates the emoji accessibility service configuration.
     */
    fun updateEmojiServiceConfiguration() {
        BatteryLogger.d(TAG, "Updating emoji accessibility service configuration")
        BatteryLogger.d(EMOJI_CONFIG_TAG, "EMOJI_CONFIG_UPDATE_REQUESTED")
        
        try {
            if (!isEmojiAccessibilityServiceRunning()) {
                BatteryLogger.d(EMOJI_CONFIG_TAG, "Emoji accessibility service not running, cannot update configuration")
                return
            }
            
            BatteryLogger.d(EMOJI_CONFIG_TAG, "Sending configuration update intent to emoji service")
            val intent = Intent(context, EmojiBatteryAccessibilityService::class.java).apply {
                action = EmojiBatteryAccessibilityService.ACTION_UPDATE_CONFIG
            }
            
            context.startService(intent)
            BatteryLogger.d(EMOJI_CONFIG_TAG, "EMOJI_CONFIG_UPDATE_INTENT_SENT")
            
        } catch (exception: Exception) {
            BatteryLogger.e(TAG, "Error updating emoji service configuration", exception)
            BatteryLogger.e(EMOJI_CONFIG_TAG, "EMOJI_CONFIG_UPDATE_FAILED: ${exception.message}")
        }
    }
    
    /**
     * Checks if the emoji battery accessibility service is currently running.
     */
    fun isEmojiAccessibilityServiceRunning(): Boolean {
        val isRunning = EmojiBatteryAccessibilityService.isServiceRunning()
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "Emoji accessibility service running status: $isRunning")
        return isRunning
    }
    
    /**
     * Gets the current emoji accessibility service instance if available.
     */
    fun getEmojiServiceInstance(): EmojiBatteryAccessibilityService? {
        val instance = EmojiBatteryAccessibilityService.getInstance()
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "Emoji service instance available: ${instance != null}")
        return instance
    }
    
    /**
     * Gets detailed emoji service status for debugging and monitoring.
     */
    fun getEmojiServiceStatus(): Map<String, Any> {
        BatteryLogger.d(TAG, "Getting emoji service status")
        val permissionStatus = EmojiOverlayPermissionManager.getPermissionStatusSummary(context)
        
        val status = mapOf(
            "isEmojiServiceRunning" to isEmojiAccessibilityServiceRunning(),
            "isMonitoringEmojiConfig" to isMonitoringEmojiConfig,
            "hasAllPermissions" to EmojiOverlayPermissionManager.hasAllRequiredPermissions(context),
            "isAccessibilityEnabled" to EmojiOverlayPermissionManager.isAccessibilityServiceEnabled(context),
            "permissionDetails" to permissionStatus
        )
        
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "Emoji service status: $status")
        return status
    }
    
    /**
     * Forces an emoji accessibility service restart if it's currently enabled.
     */
    fun restartEmojiServiceIfEnabled() {
        BatteryLogger.d(TAG, "Checking if emoji service restart is needed")
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_SERVICE_RESTART_CHECK")
        
        managerScope.launch {
            try {
                val config = customizationRepository.getCustomizationConfig()
                if (config.isGlobalEnabled) {
                    BatteryLogger.d(EMOJI_OVERLAY_TAG, "Restarting emoji service as it's enabled in configuration")
                    BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_SERVICE_RESTART_INITIATED")
                    
                    stopEmojiAccessibilityService()
                    // Small delay to ensure service stops
                    kotlinx.coroutines.delay(500)
                    startEmojiAccessibilityService()
                    
                    BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_SERVICE_RESTART_COMPLETED")
                } else {
                    BatteryLogger.d(EMOJI_CONFIG_TAG, "Emoji service not enabled in configuration, not restarting")
                }
            } catch (exception: Exception) {
                BatteryLogger.e(TAG, "Error restarting emoji service", exception)
                BatteryLogger.e(EMOJI_OVERLAY_TAG, "EMOJI_SERVICE_RESTART_FAILED: ${exception.message}")
            }
        }
    }
    
    /**
     * Handles permission changes and updates emoji service state accordingly.
     */
    fun handleEmojiPermissionChange() {
        BatteryLogger.d(TAG, "Handling emoji permission change")
        BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_PERMISSION_CHANGE_DETECTED")
        
        managerScope.launch {
            try {
                val config = customizationRepository.getCustomizationConfig()
                BatteryLogger.d(EMOJI_PERMISSION_TAG, "Current emoji config - global enabled: ${config.isGlobalEnabled}")
                handleEmojiConfigurationChange(config.isGlobalEnabled)
                BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_PERMISSION_CHANGE_HANDLED")
            } catch (exception: Exception) {
                BatteryLogger.e(TAG, "Error handling emoji permission change", exception)
                BatteryLogger.e(EMOJI_PERMISSION_TAG, "EMOJI_PERMISSION_CHANGE_FAILED: ${exception.message}")
            }
        }
    }
    
    /**
     * Cleans up resources when the manager is no longer needed.
     */
    fun cleanupEmojiManager() {
        BatteryLogger.d(TAG, "Cleaning up EmojiAccessibilityServiceManager")
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_MANAGER_CLEANUP_STARTED")
        
        isMonitoringEmojiConfig = false
        // Note: We don't cancel the managerScope as it might be needed for cleanup operations
        
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_MANAGER_CLEANUP_COMPLETED")
    }
}
