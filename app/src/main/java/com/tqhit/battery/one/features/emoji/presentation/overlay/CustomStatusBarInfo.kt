package com.tqhit.battery.one.features.emoji.presentation.overlay

import android.content.Context
import android.media.AudioManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.telephony.TelephonyManager
import com.tqhit.battery.one.utils.BatteryLogger
import java.text.SimpleDateFormat
import java.util.*

/**
 * Data class to hold custom status bar information for emoji overlay
 */
data class CustomStatusBarInfo(
    val currentTime: String,
    val wifiSignalStrength: Int, // 0-4 (0 = no signal, 4 = excellent)
    val cellularSignalStrength: Int, // 0-4 (0 = no signal, 4 = excellent)
    val isRingerSilent: <PERSON>olean,
    val hasWifiConnection: Boolean,
    val hasCellularConnection: Boolean
) {
    companion object {
        private const val TAG = "CustomStatusBarInfo"
        private const val STATUS_BAR_TAG = "StatusBar_Creation"
        
        /**
         * Creates CustomStatusBarInfo from current system state
         */
        fun fromSystemState(context: Context): CustomStatusBarInfo {
            BatteryLogger.d(STATUS_BAR_TAG, "STATUS_BAR_INFO_CREATION_STARTED")
            
            val currentTime = getCurrentTime()
            val wifiSignalStrength = getWifiSignalStrength(context)
            val cellularSignalStrength = getCellularSignalStrength(context)
            val isRingerSilent = isRingerSilent(context)
            val hasWifiConnection = hasWifiConnection(context)
            val hasCellularConnection = hasCellularConnection(context)
            
            BatteryLogger.d(STATUS_BAR_TAG, "STATUS_BAR_INFO_VALUES: time=$currentTime, wifi_signal=$wifiSignalStrength, cellular_signal=$cellularSignalStrength")
            BatteryLogger.d(STATUS_BAR_TAG, "STATUS_BAR_INFO_CONNECTIONS: wifi_connected=$hasWifiConnection, cellular_available=$hasCellularConnection, ringer_silent=$isRingerSilent")
            
            val statusBarInfo = CustomStatusBarInfo(
                currentTime = currentTime,
                wifiSignalStrength = wifiSignalStrength,
                cellularSignalStrength = cellularSignalStrength,
                isRingerSilent = isRingerSilent,
                hasWifiConnection = hasWifiConnection,
                hasCellularConnection = hasCellularConnection
            )
            
            BatteryLogger.d(STATUS_BAR_TAG, "STATUS_BAR_INFO_CREATION_COMPLETED")
            return statusBarInfo
        }
        
        private fun getCurrentTime(): String {
            val formatter = SimpleDateFormat("HH:mm", Locale.getDefault())
            val time = formatter.format(Date())
            BatteryLogger.d(STATUS_BAR_TAG, "CURRENT_TIME_OBTAINED: $time")
            return time
        }
        
        private fun getWifiSignalStrength(context: Context): Int {
            BatteryLogger.d(STATUS_BAR_TAG, "WIFI_SIGNAL_STRENGTH_CHECK_STARTED")
            try {
                val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
                val network = connectivityManager.activeNetwork
                val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
                
                if (networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true) {
                    // Return a simulated signal strength (in real implementation, would use WifiManager)
                    val strength = 3 // Good signal
                    BatteryLogger.d(STATUS_BAR_TAG, "WIFI_SIGNAL_STRENGTH_RESULT: $strength (WiFi active)")
                    return strength
                }
                
                BatteryLogger.d(STATUS_BAR_TAG, "WIFI_SIGNAL_STRENGTH_RESULT: 0 (WiFi not active)")
                return 0
            } catch (e: Exception) {
                BatteryLogger.e(STATUS_BAR_TAG, "WIFI_SIGNAL_STRENGTH_ERROR: ${e.message}")
                return 0
            }
        }
        
        private fun getCellularSignalStrength(context: Context): Int {
            BatteryLogger.d(STATUS_BAR_TAG, "CELLULAR_SIGNAL_STRENGTH_CHECK_STARTED")
            try {
                val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as? TelephonyManager
                if (telephonyManager == null) {
                    BatteryLogger.d(STATUS_BAR_TAG, "CELLULAR_SIGNAL_STRENGTH_RESULT: 0 (TelephonyManager not available)")
                    return 0
                }
                
                // Check if cellular is available based on SIM state
                val simState = try {
                    telephonyManager.simState
                } catch (e: SecurityException) {
                    BatteryLogger.d(STATUS_BAR_TAG, "CELLULAR_SIGNAL_STRENGTH_PERMISSION_ISSUE: ${e.message}")
                    TelephonyManager.SIM_STATE_UNKNOWN
                }
                
                BatteryLogger.d(STATUS_BAR_TAG, "CELLULAR_STATE_INFO: simState=$simState")
                
                // If SIM is ready, assume cellular is available
                if (simState == TelephonyManager.SIM_STATE_READY) {
                    // Return a simulated signal strength (in real implementation, would use SignalStrength)
                    val strength = 2 // Fair signal
                    BatteryLogger.d(STATUS_BAR_TAG, "CELLULAR_SIGNAL_STRENGTH_RESULT: $strength (SIM ready)")
                    return strength
                }
                
                BatteryLogger.d(STATUS_BAR_TAG, "CELLULAR_SIGNAL_STRENGTH_RESULT: 0 (SIM not ready)")
                return 0
            } catch (e: Exception) {
                BatteryLogger.e(STATUS_BAR_TAG, "CELLULAR_SIGNAL_STRENGTH_ERROR: ${e.message}")
                // Return a default signal strength to show cellular icon in case of errors
                val fallbackStrength = 1 // Minimal signal as fallback
                BatteryLogger.d(STATUS_BAR_TAG, "CELLULAR_SIGNAL_STRENGTH_FALLBACK: $fallbackStrength")
                return fallbackStrength
            }
        }
        
        private fun isRingerSilent(context: Context): Boolean {
            BatteryLogger.d(STATUS_BAR_TAG, "RINGER_MODE_CHECK_STARTED")
            try {
                val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
                val ringerMode = audioManager.ringerMode
                
                BatteryLogger.d(STATUS_BAR_TAG, "RINGER_MODE_VALUE: $ringerMode")
                
                // Show silent icon only when in silent or vibrate mode (not normal ring mode)
                val isSilent = ringerMode == AudioManager.RINGER_MODE_SILENT || ringerMode == AudioManager.RINGER_MODE_VIBRATE
                
                val modeDescription = when (ringerMode) {
                    AudioManager.RINGER_MODE_NORMAL -> "NORMAL"
                    AudioManager.RINGER_MODE_SILENT -> "SILENT"
                    AudioManager.RINGER_MODE_VIBRATE -> "VIBRATE"
                    else -> "UNKNOWN"
                }
                
                BatteryLogger.d(STATUS_BAR_TAG, "RINGER_MODE_RESULT: $isSilent (mode: $modeDescription)")
                return isSilent
            } catch (e: Exception) {
                BatteryLogger.e(STATUS_BAR_TAG, "RINGER_MODE_ERROR: ${e.message}")
                return false
            }
        }
        
        private fun hasWifiConnection(context: Context): Boolean {
            BatteryLogger.d(STATUS_BAR_TAG, "WIFI_CONNECTION_CHECK_STARTED")
            try {
                val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
                val network = connectivityManager.activeNetwork
                val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
                
                val hasWifi = networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true
                BatteryLogger.d(STATUS_BAR_TAG, "WIFI_CONNECTION_RESULT: $hasWifi")
                return hasWifi
            } catch (e: Exception) {
                BatteryLogger.e(STATUS_BAR_TAG, "WIFI_CONNECTION_ERROR: ${e.message}")
                return false
            }
        }
        
        private fun hasCellularConnection(context: Context): Boolean {
            BatteryLogger.d(STATUS_BAR_TAG, "CELLULAR_CONNECTION_CHECK_STARTED")
            try {
                val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
                val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as? TelephonyManager
                
                if (connectivityManager == null || telephonyManager == null) {
                    BatteryLogger.d(STATUS_BAR_TAG, "CELLULAR_CONNECTION_RESULT: false (system services not available)")
                    return false
                }
                
                // Check if SIM card is present and ready
                val simState = try {
                    telephonyManager.simState
                } catch (e: SecurityException) {
                    BatteryLogger.d(STATUS_BAR_TAG, "CELLULAR_CONNECTION_PERMISSION_ISSUE: ${e.message}")
                    TelephonyManager.SIM_STATE_UNKNOWN
                }
                
                BatteryLogger.d(STATUS_BAR_TAG, "CELLULAR_CONNECTION_DETAILS: simState=$simState")
                
                // Cellular is considered "available" if SIM is ready, regardless of active connection
                // This allows showing cellular icon even when WiFi is active
                val cellularAvailable = simState == TelephonyManager.SIM_STATE_READY
                
                BatteryLogger.d(STATUS_BAR_TAG, "CELLULAR_CONNECTION_RESULT: $cellularAvailable (available regardless of active connection)")
                
                // Additionally check if cellular is currently active for logging
                try {
                    val network = connectivityManager.activeNetwork
                    val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
                    val cellularActive = networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true
                    
                    BatteryLogger.d(STATUS_BAR_TAG, "CELLULAR_CONNECTION_ACTIVE: $cellularActive (currently active connection)")
                } catch (e: Exception) {
                    BatteryLogger.d(STATUS_BAR_TAG, "CELLULAR_CONNECTION_ACTIVE_CHECK_FAILED: ${e.message}")
                }
                
                return cellularAvailable
            } catch (e: Exception) {
                BatteryLogger.e(STATUS_BAR_TAG, "CELLULAR_CONNECTION_ERROR: ${e.message}")
                // Return true as fallback to show cellular icon when we can't determine state
                BatteryLogger.d(STATUS_BAR_TAG, "CELLULAR_CONNECTION_FALLBACK: true (showing icon due to uncertainty)")
                return true
            }
        }
    }
}
