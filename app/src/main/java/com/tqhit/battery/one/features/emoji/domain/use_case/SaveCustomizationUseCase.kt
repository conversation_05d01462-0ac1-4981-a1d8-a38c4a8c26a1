package com.tqhit.battery.one.features.emoji.domain.use_case

import android.util.Log
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import javax.inject.Inject

/**
 * Use case for saving emoji battery customization configuration.
 * Handles validation and business logic for customization persistence.
 * 
 * This use case follows the established patterns in the app:
 * - Single responsibility principle
 * - Proper error handling and logging
 * - Input validation
 * - Clean architecture separation
 */
class SaveCustomizationUseCase @Inject constructor(
    private val customizationRepository: CustomizationRepository
) {
    
    companion object {
        private const val TAG = "SaveCustomizationUseCase"
    }
    
    /**
     * Saves the customization configuration with validation.
     * 
     * @param config The CustomizationConfig to save
     * @return Result indicating success or failure with error details
     */
    suspend operator fun invoke(config: CustomizationConfig): Result<Unit> {
        Log.d(TAG, "Saving customization config for style: ${config.selectedStyleId}")
        Log.d(TAG, "SAVE_DEBUG: ========== SAVE CUSTOMIZATION USE CASE STARTED ==========")
        Log.d(TAG, "SAVE_DEBUG: Input config - selectedStyleId: ${config.selectedStyleId}")
        Log.d(TAG, "SAVE_DEBUG: Input config - isGlobalEnabled: ${config.isGlobalEnabled}")
        Log.d(TAG, "SAVE_DEBUG: Input config - showEmoji: ${config.customConfig.showEmoji}")
        Log.d(TAG, "SAVE_DEBUG: Input config - showPercentage: ${config.customConfig.showPercentage}")

        return try {
            // Validate the configuration
            Log.d(TAG, "SAVE_DEBUG: Validating configuration...")
            if (!config.isValid()) {
                val errorMessage = "Invalid customization configuration: ${config}"
                Log.w(TAG, errorMessage)
                Log.w(TAG, "SAVE_DEBUG: Configuration validation failed!")
                return Result.failure(IllegalArgumentException(errorMessage))
            }
            Log.d(TAG, "SAVE_DEBUG: Configuration validation passed")

            // Ensure the configuration is validated and has current timestamp
            val validatedConfig = config.validated().withUpdatedTimestamp()
            Log.d(TAG, "SAVE_DEBUG: Created validated config with timestamp: ${validatedConfig.lastModifiedTimestamp}")

            // Save through repository
            Log.d(TAG, "SAVE_DEBUG: Calling repository.saveCustomizationConfig...")
            val result = customizationRepository.saveCustomizationConfig(validatedConfig)

            if (result.isSuccess) {
                Log.d(TAG, "Successfully saved customization config for style: ${validatedConfig.selectedStyleId}")
                Log.d(TAG, "SAVE_DEBUG: Repository save successful!")
            } else {
                Log.e(TAG, "Failed to save customization config", result.exceptionOrNull())
                Log.e(TAG, "SAVE_DEBUG: Repository save failed: ${result.exceptionOrNull()?.message}")
            }

            Log.d(TAG, "SAVE_DEBUG: Returning result: ${if (result.isSuccess) "SUCCESS" else "FAILURE"}")
            Log.d(TAG, "SAVE_DEBUG: ========== SAVE CUSTOMIZATION USE CASE COMPLETED ==========")
            result
        } catch (exception: Exception) {
            Log.e(TAG, "Error in SaveCustomizationUseCase", exception)
            Log.e(TAG, "SAVE_DEBUG: Exception in save use case: ${exception.message}", exception)
            Log.d(TAG, "SAVE_DEBUG: ========== SAVE CUSTOMIZATION USE CASE FAILED ==========")
            Result.failure(exception)
        }
    }
    
    /**
     * Saves only the selected style ID while preserving other settings.
     * Convenience method for style selection.
     * 
     * @param styleId The ID of the selected battery style
     * @return Result indicating success or failure
     */
    suspend fun saveSelectedStyle(styleId: String): Result<Unit> {
        Log.d(TAG, "Saving selected style: $styleId")
        
        return try {
            if (styleId.isBlank()) {
                val errorMessage = "Style ID cannot be blank"
                Log.w(TAG, errorMessage)
                return Result.failure(IllegalArgumentException(errorMessage))
            }
            
            val result = customizationRepository.updateSelectedStyle(styleId)
            
            if (result.isSuccess) {
                Log.d(TAG, "Successfully saved selected style: $styleId")
            } else {
                Log.e(TAG, "Failed to save selected style: $styleId", result.exceptionOrNull())
            }
            
            result
        } catch (exception: Exception) {
            Log.e(TAG, "Error saving selected style", exception)
            Result.failure(exception)
        }
    }
    
    /**
     * Saves the global enabled state while preserving other settings.
     * Convenience method for the global toggle.
     * 
     * @param isEnabled Whether the emoji battery feature is globally enabled
     * @return Result indicating success or failure
     */
    suspend fun saveGlobalEnabled(isEnabled: Boolean): Result<Unit> {
        Log.d(TAG, "Saving global enabled state: $isEnabled")
        
        return try {
            val result = customizationRepository.updateGlobalEnabled(isEnabled)
            
            if (result.isSuccess) {
                Log.d(TAG, "Successfully saved global enabled state: $isEnabled")
            } else {
                Log.e(TAG, "Failed to save global enabled state: $isEnabled", result.exceptionOrNull())
            }
            
            result
        } catch (exception: Exception) {
            Log.e(TAG, "Error saving global enabled state", exception)
            Result.failure(exception)
        }
    }
    
    /**
     * Clears all customization data and resets to defaults.
     * 
     * @return Result indicating success or failure
     */
    suspend fun clearCustomization(): Result<Unit> {
        Log.d(TAG, "Clearing all customization data")
        
        return try {
            val result = customizationRepository.clearCustomization()
            
            if (result.isSuccess) {
                Log.d(TAG, "Successfully cleared customization data")
            } else {
                Log.e(TAG, "Failed to clear customization data", result.exceptionOrNull())
            }
            
            result
        } catch (exception: Exception) {
            Log.e(TAG, "Error clearing customization data", exception)
            Result.failure(exception)
        }
    }
}
