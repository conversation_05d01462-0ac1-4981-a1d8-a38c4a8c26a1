package com.tqhit.battery.one.ads.core

import android.content.Context
import android.os.Handler
import android.os.Looper
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdRevenueListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.nativeAds.MaxNativeAdListener
import com.applovin.mediation.nativeAds.MaxNativeAdLoader
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.google.firebase.analytics.FirebaseAnalytics
import com.tqhit.adlib.sdk.analytics.AnalyticsTracker
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ApplovinNativeAdManager @Inject constructor(
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    @ApplicationContext private val context: Context,
    private val analyticsTracker: AnalyticsTracker
    ): MaxAdRevenueListener {
    private val adUnitId = "b1aebeb926a91949"
    private val waitTime = 1000L
    private val maxRetryCount = 6

    private var nativeAdLoader: MaxNativeAdLoader? = null
    private val cachedNativeAd = mutableListOf<MaxAd?>()

    private fun initNativeAdLoader() {

        nativeAdLoader = MaxNativeAdLoader(adUnitId)
        nativeAdLoader?.setRevenueListener(this)
    }

    fun loadNativeAd(
        onAdLoaded: (MaxAd) -> Unit,
        onAdLoadFailed: (String) -> Unit,
        retryCount: Int = 0,
    ) {
        val ntEnable = remoteConfigHelper.getBoolean("nt_enable")
        if (ntEnable) {
            initNativeAdLoader()
            if (cachedNativeAd.size > 0) {
                val cachedNativeAd = getCachedNativeAd()
                if (cachedNativeAd != null) {
                    onAdLoaded(cachedNativeAd)
                    return
                }
            }
            nativeAdLoader?.setNativeAdListener(object : MaxNativeAdListener() {
                override fun onNativeAdLoaded(view: MaxNativeAdView?, ad: MaxAd) {
                    addCachedNativeAd(ad)
                    onAdLoaded(ad)
                    analyticsTracker.logEvent("native_load_success", mapOf("placement" to ad.placement))
                }

                override fun onNativeAdLoadFailed(adUnitId: String, errorCode: MaxError) {
                    onAdLoadFailed("Native ad failed to load: $errorCode")
                    val retryDelay = calculateWaitTime(retryCount)
                    if (retryDelay < 0) return
                    Handler(Looper.getMainLooper()).postDelayed({
                        loadNativeAd(
                            onAdLoaded = onAdLoaded,
                            onAdLoadFailed = onAdLoadFailed,
                            retryCount = retryCount + 1
                        )
                    }, retryDelay)
                    analyticsTracker.logEvent("native_load_fail")
                }

                override fun onNativeAdClicked(ad: MaxAd) {
                    super.onNativeAdClicked(ad)
                    analyticsTracker.logEvent("native_click", mapOf("placement" to ad.placement))
                }
            })
            analyticsTracker.logEvent("native_load")
            nativeAdLoader?.loadAd()
        }
    }

    fun addCachedNativeAd(nativeAd: MaxAd?) {
        cachedNativeAd.add(nativeAd)
    }

    fun removeCachedNativeAd(nativeAd: MaxAd?) {
        cachedNativeAd.remove(nativeAd)
    }

    private fun getCachedNativeAd(): MaxAd? {
        if (cachedNativeAd.isNotEmpty()) {
            return cachedNativeAd[0]
        }
        return null
    }

    fun render(
        nativeAd: MaxAd,
        nativeAdView: MaxNativeAdView
    ) : Boolean {
        return nativeAdLoader?.render(nativeAdView, nativeAd) ?: false
    }

    override fun onAdRevenuePaid(impressionData: MaxAd) {
        impressionData.let {
            analyticsTracker.logEvent(
                FirebaseAnalytics.Event.AD_IMPRESSION,
                mapOf(
                    FirebaseAnalytics.Param.AD_PLATFORM to "appLovin",
                    FirebaseAnalytics.Param.AD_UNIT_NAME to impressionData.adUnitId,
                    FirebaseAnalytics.Param.AD_FORMAT to impressionData.format.label,
                    FirebaseAnalytics.Param.AD_SOURCE to impressionData.networkName,
                    FirebaseAnalytics.Param.VALUE to impressionData.revenue,
                    FirebaseAnalytics.Param.CURRENCY to "USD",
                    "placement" to impressionData.placement,
                )
            )
            analyticsTracker.logEvent(
                "ad_impression_custom",
                mapOf(
                    FirebaseAnalytics.Param.AD_PLATFORM to "appLovin",
                    FirebaseAnalytics.Param.AD_UNIT_NAME to impressionData.adUnitId,
                    FirebaseAnalytics.Param.AD_FORMAT to impressionData.format.label,
                    FirebaseAnalytics.Param.AD_SOURCE to impressionData.networkName,
                    FirebaseAnalytics.Param.VALUE to impressionData.revenue,
                    FirebaseAnalytics.Param.CURRENCY to "USD",
                    "placement" to impressionData.placement,
                    "impression_count" to 1,
                )
            )
        }
    }

    fun calculateWaitTime(
        retryCount: Int
    ): Long {
        if (retryCount < 0 || retryCount > maxRetryCount) {
            return -1L
        }
        return waitTime * (1 shl retryCount)
    }
}