package com.tqhit.battery.one.features.emoji.domain.model

import android.util.Log

/**
 * Enumeration of battery style categories for the emoji battery feature.
 * Defines different categories to organize and filter battery styles.
 * 
 * Each category has a display name and emoji icon for UI presentation.
 */
enum class BatteryStyleCategory(
    val displayName: String,
    val emoji: String,
    val sortOrder: Int
) {
    /**
     * Hot/trending styles - featured and popular content
     */
    HOT("HOT", "🔥", 0),
    
    /**
     * Character-based styles - anime, cartoon, mascot characters
     */
    CHARACTER("Character", "👾", 1),
    
    /**
     * Heart-themed styles - romantic, love-themed designs
     */
    HEART("Heart", "❤️", 2),
    
    /**
     * Cute styles - kawaii, adorable designs
     */
    CUTE("Cute", "🥰", 3),
    
    /**
     * Animal-themed styles - pets, wildlife, creatures
     */
    ANIMAL("Animal", "🐱", 4),
    
    /**
     * Food-themed styles - fruits, snacks, beverages
     */
    FOOD("Food", "🍎", 5),
    
    /**
     * Nature-themed styles - plants, weather, landscapes
     */
    NATURE("Nature", "🌸", 6),
    
    /**
     * Gaming-themed styles - game characters, controllers, symbols
     */
    GAMING("Gaming", "🎮", 7),
    
    /**
     * Seasonal styles - holiday, seasonal themes
     */
    SEASONAL("Seasonal", "🎄", 8),
    
    /**
     * Minimalist styles - simple, clean designs
     */
    MINIMAL("Minimal", "⚪", 9),

    /**
     * Brainrot styles - trending internet culture content
     */
    BRAINROT("Brainrot", "🧠", 10),

    /**
     * 3D Sticker styles - three-dimensional sticker designs
     */
    STICKER_3D("Sticker 3D", "🎨", 11),

    /**
     * Emotion styles - emotional expressions and feelings
     */
    EMOTION("Emotion", "😊", 12),

    /**
     * Other styles - miscellaneous and uncategorized content
     */
    OTHER("Other", "📦", 13);
    
    /**
     * Gets the category display text with emoji.
     * 
     * @return Formatted display text (e.g., "🔥 HOT")
     */
    fun getDisplayText(): String {
        return "$emoji $displayName"
    }
    
    /**
     * Checks if this category should be featured prominently.
     * HOT category is always featured.
     * 
     * @return true if this category should be featured
     */
    fun isFeatured(): Boolean {
        return this == HOT
    }
    
    companion object {
        /**
         * Gets all categories sorted by their display order.
         * 
         * @return List of categories in display order
         */
        fun getAllSorted(): List<BatteryStyleCategory> {
            return values().sortedBy { it.sortOrder }
        }
        
        /**
         * Gets categories that should be shown in the main filter tabs.
         * Excludes less common categories to avoid UI clutter.
         * 
         * @return List of main filter categories
         */
        fun getMainFilterCategories(): List<BatteryStyleCategory> {
            return listOf(HOT, CHARACTER, HEART, CUTE, ANIMAL, FOOD)
        }
        
        /**
         * Finds a category by its display name (case-insensitive).
         * 
         * @param displayName The display name to search for
         * @return The matching category, or null if not found
         */
        fun findByDisplayName(displayName: String): BatteryStyleCategory? {
            return values().find { 
                it.displayName.equals(displayName, ignoreCase = true) 
            }
        }
        
        /**
         * Gets the default category for new styles.
         * 
         * @return Default category (CHARACTER)
         */
        fun getDefault(): BatteryStyleCategory {
            return CHARACTER
        }
        
        /**
         * Creates a category from a string value, with fallback to default.
         * Used for parsing data from remote config or local storage.
         *
         * @param value String representation of the category
         * @return The matching category, or default if not found
         */
        fun fromString(value: String?): BatteryStyleCategory {
            if (value.isNullOrBlank()) return getDefault()

            return try {
                valueOf(value.uppercase())
            } catch (e: IllegalArgumentException) {
                findByDisplayName(value) ?: getDefault()
            }
        }

        /**
         * Maps remote config category ID to BatteryStyleCategory.
         * Used to convert Firebase Remote Config category IDs to enum values.
         *
         * @param categoryId The category ID from remote config
         * @return The matching category, or null if not supported in enum yet
         */
        fun fromCategoryId(categoryId: String): BatteryStyleCategory? {
            val normalizedId = categoryId.lowercase()
            val result = when (normalizedId) {
                "hot_category" -> HOT
                "character_category" -> CHARACTER
                "heart_category" -> HEART
                "cute_category", "cute-category" -> CUTE  // Support both underscore and hyphen formats
                "animal_category" -> ANIMAL
                "food_category" -> FOOD
                "nature_category" -> NATURE
                "gaming_category" -> GAMING
                "seasonal_category" -> SEASONAL
                "minimal_category" -> MINIMAL
                "brainrot_category" -> BRAINROT
                "sticker3d_category" -> STICKER_3D
                "emotion_category" -> EMOTION
                "other_category" -> OTHER
                else -> null
            }

            // Add comprehensive logging to track reverse category mapping
            if (result != null) {
                Log.d("CATEGORY_MAPPING", "Mapped remote config key '$categoryId' -> UI category: ${result.displayName}")
            } else {
                Log.w("CATEGORY_MAPPING", "No mapping found for remote config key: '$categoryId'")
            }

            return result
        }
    }
}

/**
 * Extension function to map BatteryStyleCategory to remote config category ID.
 * Used to fetch emoji items from Firebase Remote Config by category.
 *
 * @return The corresponding remote config category ID
 */
fun BatteryStyleCategory.toCategoryId(): String {
    val categoryId = when (this) {
        BatteryStyleCategory.HOT -> "hot_category"
        BatteryStyleCategory.CHARACTER -> "character_category"
        BatteryStyleCategory.HEART -> "heart_category"
        BatteryStyleCategory.CUTE -> "cute_category"
        BatteryStyleCategory.ANIMAL -> "animal_category"
        BatteryStyleCategory.FOOD -> "food_category"
        BatteryStyleCategory.NATURE -> "nature_category"
        BatteryStyleCategory.GAMING -> "gaming_category"
        BatteryStyleCategory.SEASONAL -> "seasonal_category"
        BatteryStyleCategory.MINIMAL -> "minimal_category"
        BatteryStyleCategory.BRAINROT -> "brainrot_category"
        BatteryStyleCategory.STICKER_3D -> "sticker3d_category"
        BatteryStyleCategory.EMOTION -> "emotion_category"
        BatteryStyleCategory.OTHER -> "other_category"
    }

    // Add comprehensive logging to track category mapping
    Log.d("CATEGORY_MAPPING", "Mapped UI category ${this.displayName} -> remote config key: $categoryId")

    return categoryId
}
