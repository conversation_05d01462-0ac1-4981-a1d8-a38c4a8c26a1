# Emoji Battery Feature: Testing Plan

**Author:** Gemini
**Date:** July 6, 2025
**Status:** Updated for Phase 4.4

## 1. Overview

This document outlines the comprehensive testing strategy for the Emoji Battery feature, focusing on robust unit and instrumentation tests to ensure quality and prevent regressions. The primary challenge is testing UI components and services that rely on the Android framework and external dependencies like Firebase.

Our strategy is to use a combination of **Robolectric** for framework-aware unit tests and **Espresso** for on-device instrumentation tests.

## 2. Tools and Frameworks

- **JUnit 4/5:** The core testing framework.
- **Robolectric:** For running Android framework-dependent tests on the JVM. It simulates the Android environment, allowing access to resources, themes, and views without an emulator.
- **Mockito/MockK:** For mocking dependencies and verifying interactions.
- **HiltAndroidTest:** For testing Hilt dependency injection.
- **Turbine:** For testing Kotlin Flows.
- **Espresso:** For UI instrumentation tests that run on a device or emulator.

## 3. Unit Testing Strategy (Phase 4)

### Task 4.1: `EmojiBatteryView.kt` Unit Tests

**Status:** ✅ Implemented

**Strategy:** Use Robolectric to provide a real `ApplicationProvider.getApplicationContext()`. This solves the problem of the view requiring a real context to access resources (`R.color`, `R.dimen`, themes) and allows for proper inflation and measurement.

**Key Test Cases:**
-   **Configuration Updates:** Verify that calling `updateCustomizationConfig` correctly applies properties like color and font size.
-   **Battery Level Updates:** Ensure the view correctly renders different battery levels and charging states when `updateBatteryStatus` is called.
-   **Visibility Toggles:** Test that the view correctly handles configurations where the emoji or percentage text is hidden.
-   **Gesture Detection:** While full gesture simulation is complex, we can test the `handleGestureResult` logic by passing it mock `GestureResult` objects and verifying that the correct `NotificationActionCallback` methods are invoked.

---

### Task 4.2: `EmojiBatteryAccessibilityService.kt` - Service Logic & Gesture Handling

**Problem:** Testing an `AccessibilityService` is complex due to its lifecycle, its interaction with the `WindowManager`, and its dependency on system-level actions.

**Solution:** Use a combination of Robolectric's `ServiceController`, Hilt for dependency injection, and MockK to mock dependencies and verify interactions.

**Test Implementation (`EmojiBatteryAccessibilityServiceTest.kt`):**

```kotlin
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
@Config(application = HiltTestApplication::class)
class EmojiBatteryAccessibilityServiceTest {

    @get:Rule
    var hiltRule = HiltAndroidRule(this)

    @Mock
    private lateinit var mockWindowManager: WindowManager

    @Mock
    private lateinit var mockEmojiBatteryView: EmojiBatteryView
    
    private lateinit var serviceController: ServiceController<EmojiBatteryAccessibilityService>
    private lateinit var service: EmojiBatteryAccessibilityService

    @Before
    fun setUp() {
        hiltRule.inject()
        // Mock the WindowManager
        Shadows.shadowOf(ApplicationProvider.getApplicationContext()).setSystemService(Context.WINDOW_SERVICE, mockWindowManager)
        
        serviceController = Robolectric.buildService(EmojiBatteryAccessibilityService::class.java)
        service = serviceController.get()
    }

    @Test
    fun `onServiceConnected should add view to window manager if enabled`() {
        // Arrange: Assume config is enabled
        // Act
        serviceController.create().get().onServiceConnected()

        // Assert
        verify(mockWindowManager).addView(any(), any())
    }

    @Test
    fun `onNotificationActionRequested with left swipe should perform notifications action`() {
        // Arrange
        val serviceSpy = spyk(service, recordPrivateCalls = true)

        // Act
        serviceSpy.onNotificationActionRequested(EmojiGestureHelper.GestureType.SWIPE_DOWN_LEFT)

        // Assert
        verify { serviceSpy.performGlobalAction(AccessibilityService.GLOBAL_ACTION_NOTIFICATIONS) }
    }
    
    @Test
    fun `onNotificationActionRequested with right swipe should perform quick settings action`() {
        // Arrange
        val serviceSpy = spyk(service, recordPrivateCalls = true)

        // Act
        serviceSpy.onNotificationActionRequested(EmojiGestureHelper.GestureType.SWIPE_DOWN_RIGHT)

        // Assert
        verify { serviceSpy.performGlobalAction(AccessibilityService.GLOBAL_ACTION_QUICK_SETTINGS) }
    }
}
```

---

### Task 4.3: `EmojiBatteryAccessibilityService.kt` - Live Data Integration

**Problem:** Testing the integration of live data from `Flow` objects within a service requires controlling the flow emissions and verifying the service's reactions in a structured way.

**Solution:** Use `@HiltAndroidTest` to provide mock versions of the data providers (`CoreBatteryStatsProvider`, `LoadCustomizationUseCase`). Use the `Turbine` library to test the `Flow` collection logic within the service's coroutine scope.

**Test Implementation (`EmojiBatteryAccessibilityServiceTest.kt`):**

```kotlin
// (Inside the Hilt-enabled test class)

@BindValue @Mock
lateinit var mockCoreBatteryStatsProvider: CoreBatteryStatsProvider

@BindValue @Mock
lateinit var mockLoadCustomizationUseCase: LoadCustomizationUseCase

private val batteryStatusFlow = MutableStateFlow(CoreBatteryStatus.createDefault())
private val customizationConfigFlow = MutableStateFlow(CustomizationConfig.createDefault())

@Before
fun setupFlows() {
    // Configure mocks to return our test flows
    whenever(mockCoreBatteryStatsProvider.coreBatteryStatusFlow).thenReturn(batteryStatusFlow)
    whenever(mockLoadCustomizationUseCase.invoke()).thenReturn(customizationConfigFlow)
}

@Test
fun `service should update view on new battery status`() = runTest {
    // Arrange
    serviceController.create().get().onServiceConnected()
    val newStatus = CoreBatteryStatus.createDefault().copy(percentage = 88)

    // Act
    batteryStatusFlow.emit(newStatus)
    advanceUntilIdle() // Allow the collector to process the new emission

    // Assert
    // Verify that the service called the update method on its view instance.
    // This requires having a reference to the view, which can be tricky.
    // An alternative is to use an ArgumentCaptor on windowManager.addView()
    // to capture the view instance and then verify calls on it.
}

@Test
fun `service should show overlay when global config is enabled`() = runTest {
    // Arrange
    serviceController.create().get().onServiceConnected()
    val newConfig = CustomizationConfig.createDefault().copy(isGlobalEnabled = true)

    // Act
    customizationConfigFlow.emit(newConfig)
    advanceUntilIdle()

    // Assert
    verify(mockWindowManager).addView(any(), any())
}

@Test
fun `service should hide overlay when global config is disabled`() = runTest {
    // Arrange
    // First, show the view
    customizationConfigFlow.emit(CustomizationConfig.createDefault().copy(isGlobalEnabled = true))
    serviceController.create().get().onServiceConnected()
    advanceUntilIdle()
    verify(mockWindowManager).addView(any(), any())

    // Act: Now, disable it
    val newConfig = CustomizationConfig.createDefault().copy(isGlobalEnabled = false)
    customizationConfigFlow.emit(newConfig)
    advanceUntilIdle()

    // Assert
    verify(mockWindowManager).removeView(any())
}
```

---

### Task 4.4: `CustomizeViewModel.kt` - Permission & Service Lifecycle Control

**Problem:** Unit testing the ViewModel's interaction with Android framework components like `Context.startService()` and permission checks is challenging in a pure JUnit environment due to `viewModelScope` and `init` block coroutines.

**Solution:** Use **Instrumentation Tests** for this task. This allows for a more realistic testing environment where Android framework components are available. We will use Hilt for dependency injection and MockK for mocking specific dependencies.

**Test Implementation (`CustomizeViewModelInstrumentationTest.kt`):**

```kotlin
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
@Config(application = HiltTestApplication::class)
class CustomizeViewModelInstrumentationTest {

    @get:Rule
    var hiltRule = HiltAndroidRule(this)

    @BindValue @Mock
    lateinit var mockLoadCustomizationUseCase: LoadCustomizationUseCase

    @BindValue @Mock
    lateinit var mockSaveCustomizationUseCase: SaveCustomizationUseCase

    @BindValue @Mock
    lateinit var mockEmojiItemService: EmojiItemService

    @BindValue @Mock
    lateinit var mockCustomizationRepository: CustomizationRepository

    @ApplicationContext
    @Inject
    lateinit var context: Context

    private lateinit var viewModel: CustomizeViewModel

    @Before
    fun setUp() {
        hiltRule.inject()

        // Mock default behavior for use cases and repository
        coEvery { mockLoadCustomizationUseCase.invoke() } returns flowOf(CustomizationConfig.createDefault())
        coEvery { mockCustomizationRepository.updateGlobalEnabled(any()) } returns Result.success(Unit)

        viewModel = CustomizeViewModel(
            loadCustomizationUseCase = mockLoadCustomizationUseCase,
            saveCustomizationUseCase = mockSaveCustomizationUseCase,
            emojiItemService = mockEmojiItemService,
            customizationRepository = mockCustomizationRepository,
            context = context
        )
    }

    @Test
    fun `toggleGlobalEnabled true with permissions should start service`() = runTest {
        // Arrange
        mockkObject(EmojiOverlayPermissionManager)
        every { EmojiOverlayPermissionManager.hasAllRequiredPermissions(context) } returns true

        // Spy on the context to verify startService calls
        val contextSpy = spyk(context)
        // Re-initialize ViewModel with the spied context
        viewModel = CustomizeViewModel(
            loadCustomizationUseCase = mockLoadCustomizationUseCase,
            saveCustomizationUseCase = mockSaveCustomizationUseCase,
            emojiItemService = mockEmojiItemService,
            customizationRepository = mockCustomizationRepository,
            context = contextSpy
        )

        // Act
        viewModel.handleEvent(CustomizeEvent.ToggleGlobalEnabled(true))
        advanceUntilIdle()

        // Assert
        verify { contextSpy.startService(match<Intent> { it.action == EmojiBatteryAccessibilityService.ACTION_SHOW_OVERLAY }) }
        coVerify { mockCustomizationRepository.updateGlobalEnabled(true) }
        assertEquals(true, viewModel.uiState.value.isGlobalEnabled)
    }

    @Test
    fun `toggleGlobalEnabled false should stop service`() = runTest {
        // Arrange
        // Simulate it's currently enabled
        viewModel.handleEvent(CustomizeEvent.ToggleGlobalEnabled(true))
        advanceUntilIdle()
        assertEquals(true, viewModel.uiState.value.isGlobalEnabled)

        // Spy on the context to verify startService calls
        val contextSpy = spyk(context)
        // Re-initialize ViewModel with the spied context
        viewModel = CustomizeViewModel(
            loadCustomizationUseCase = mockLoadCustomizationUseCase,
            saveCustomizationUseCase = mockSaveCustomizationUseCase,
            emojiItemService = mockEmojiItemService,
            customizationRepository = mockCustomizationRepository,
            context = contextSpy
        )

        // Act
        viewModel.handleEvent(CustomizeEvent.ToggleGlobalEnabled(false))
        advanceUntilIdle()

        // Assert
        verify { contextSpy.startService(match { it.action == EmojiBatteryAccessibilityService.ACTION_HIDE_OVERLAY }) }
        coVerify { mockCustomizationRepository.updateGlobalEnabled(false) }
        assertEquals(false, viewModel.uiState.value.isGlobalEnabled)
    }

    @Test
    fun `handlePermissionResult granted should enable feature and start service`() = runTest {
        // Arrange
        // Simulate initial state where permissions were requested
        viewModel.handleEvent(CustomizeEvent.ToggleGlobalEnabled(true))
        advanceUntilIdle()
        assertEquals(true, viewModel.uiState.value.shouldRequestPermissions)

        // Spy on the context to verify startService calls
        val contextSpy = spyk(context)
        // Re-initialize ViewModel with the spied context
        viewModel = CustomizeViewModel(
            loadCustomizationUseCase = mockLoadCustomizationUseCase,
            saveCustomizationUseCase = mockSaveCustomizationUseCase,
            emojiItemService = mockEmojiItemService,
            customizationRepository = mockCustomizationRepository,
            context = contextSpy
        )

        // Act
        every { EmojiOverlayPermissionManager.hasAllRequiredPermissions(context) } returns true // Simulate permission granted
        viewModel.handleEvent(CustomizeEvent.HandlePermissionResult(true))
        advanceUntilIdle()

        // Assert
        verify { contextSpy.startService(match { it.action == EmojiBatteryAccessibilityService.ACTION_SHOW_OVERLAY }) }
        coVerify { mockCustomizationRepository.updateGlobalEnabled(true) }
        assertEquals(true, viewModel.uiState.value.isGlobalEnabled)
        assertEquals(false, viewModel.uiState.value.shouldRequestPermissions)
    }

    @Test
    fun `handlePermissionResult denied should disable feature`() = runTest {
        // Arrange
        // Simulate initial state where permissions were requested
        viewModel.handleEvent(CustomizeEvent.ToggleGlobalEnabled(true))
        advanceUntilIdle()
        assertEquals(true, viewModel.uiState.value.shouldRequestPermissions)

        // Spy on the context to verify startService calls
        val contextSpy = spyk(context)
        // Re-initialize ViewModel with the spied context
        viewModel = CustomizeViewModel(
            loadCustomizationUseCase = mockLoadCustomizationUseCase,
            saveCustomizationUseCase = mockSaveCustomizationUseCase,
            emojiItemService = mockEmojiItemService,
            customizationRepository = mockCustomizationRepository,
            context = contextSpy
        )

        // Act
        viewModel.handleEvent(CustomizeEvent.HandlePermissionResult(false))
        advanceUntilIdle()

        // Assert
        verify(inverse = true) { contextSpy.startService(any()) } // Service should not be started
        coVerify(inverse = true) { mockCustomizationRepository.updateGlobalEnabled(true) } // Should not try to enable
        assertEquals(false, viewModel.uiState.value.isGlobalEnabled)
        assertEquals(false, viewModel.uiState.value.shouldRequestPermissions)
    }
}
```

## 4. Next Steps

1.  **Implement Tests:** Write the unit tests for phases 4.2 and 4.3 as described above. **Implement instrumentation tests for Phase 4.4.**
2.  **Add Dependencies:** Ensure `build.gradle.kts` has the necessary Robolectric, Hilt, MockK, and Turbine testing dependencies.
3.  **Run Tests:** Execute the tests to ensure the service logic and data integration are working correctly.
4.  **Instrumentation Tests:** Once unit tests are stable, create a simple Espresso test to verify that the overlay appears on a real device/emulator when the feature is enabled. This will be a final end-to-end validation.
5.  **Follow this Plan:** Use this document as a guide for writing tests for the rest of the feature.