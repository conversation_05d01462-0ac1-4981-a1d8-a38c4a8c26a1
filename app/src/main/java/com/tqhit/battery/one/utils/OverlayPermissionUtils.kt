package com.tqhit.battery.one.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.core.net.toUri
import com.tqhit.battery.one.R
import com.tqhit.battery.one.dialog.utils.NotificationDialog

/**
 * Utility class for managing overlay permissions.
 * Provides centralized overlay permission checking and dialog management
 * following the established stats module architecture pattern.
 */
object OverlayPermissionUtils {
    
    private const val TAG = "OverlayPermissionUtils"
    private const val OVERLAY_TAG = "OverlayPermission_Check"
    
    /**
     * Checks if the overlay permission is granted for the given context.
     * 
     * @param context The application context
     * @return true if overlay permission is granted, false otherwise
     */
    fun isOverlayPermissionGranted(context: Context): Boolean {
        val isGranted = Settings.canDrawOverlays(context)
        BatteryLogger.d(OVERLAY_TAG, "Overlay permission status: $isGranted")
        return isGranted
    }
    
    /**
     * Shows the overlay permission request dialog with consistent styling.
     *
     * @param context The activity context
     * @param onConfirm Callback when user confirms to grant permission
     * @param onCancel Callback when user cancels the permission request
     */
    fun showOverlayPermissionDialog(
        context: Context,
        onConfirm: () -> Unit = {},
        onCancel: () -> Unit = {}
    ) {
        BatteryLogger.d(TAG, "Showing overlay permission dialog")

        // Check if context is valid and activity is not finishing/destroyed
        if (!isContextValid(context)) {
            BatteryLogger.w(TAG, "Context is not valid for showing overlay permission dialog")
            return
        }

        try {
            NotificationDialog(
                context = context,
                title = context.getString(R.string.overlay_permission_title),
                message = context.getString(R.string.overlay_permission_message),
                onConfirm = {
                    BatteryLogger.d(TAG, "User confirmed overlay permission request")
                    onConfirm()
                },
                onCancel = {
                    BatteryLogger.d(TAG, "User cancelled overlay permission request")
                    onCancel()
                }
            ).show()
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to show overlay permission dialog", e)
            // If dialog fails to show, call onCancel to handle the failure gracefully
            try {
                onCancel()
            } catch (callbackException: Exception) {
                BatteryLogger.e(TAG, "Error in onCancel callback", callbackException)
            }
        }
    }

    /**
     * Checks if the provided context is valid for showing dialogs.
     *
     * @param context The context to validate
     * @return true if context is valid, false otherwise
     */
    private fun isContextValid(context: Context): Boolean {
        return when (context) {
            is android.app.Activity -> {
                !context.isFinishing && !context.isDestroyed
            }
            is androidx.fragment.app.FragmentActivity -> {
                !context.isFinishing && !context.isDestroyed
            }
            else -> {
                // For other context types, assume valid but log warning
                BatteryLogger.w(TAG, "Context type ${context.javaClass.simpleName} may not be suitable for dialogs")
                true
            }
        }
    }
    
    /**
     * Creates an intent to open the overlay permission settings page.
     * 
     * @param context The application context
     * @return Intent to open overlay permission settings
     */
    fun createOverlayPermissionIntent(context: Context): Intent {
        BatteryLogger.d(TAG, "Creating overlay permission intent for package: ${context.packageName}")
        
        return Intent(
            Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
            ("package:" + context.packageName).toUri()
        )
    }
    
    /**
     * Opens the overlay permission settings page directly.
     * 
     * @param context The application context
     */
    fun openOverlayPermissionSettings(context: Context) {
        BatteryLogger.d(TAG, "Opening overlay permission settings")
        
        try {
            val intent = createOverlayPermissionIntent(context)
            context.startActivity(intent)
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to open overlay permission settings", e)
        }
    }
    
    /**
     * Shows overlay permission dialog and handles the permission request flow.
     * 
     * @param context The activity context
     * @param onPermissionGranted Callback when permission is already granted or user grants it
     * @param onPermissionDenied Callback when permission is denied
     */
    fun requestOverlayPermissionIfNeeded(
        context: Context,
        onPermissionGranted: () -> Unit = {},
        onPermissionDenied: () -> Unit = {}
    ) {
        BatteryLogger.d(TAG, "Checking overlay permission status")
        
        if (isOverlayPermissionGranted(context)) {
            BatteryLogger.d(TAG, "Overlay permission already granted")
            onPermissionGranted()
            return
        }
        
        BatteryLogger.d(TAG, "Overlay permission not granted, showing dialog")
        showOverlayPermissionDialog(
            context = context,
            onConfirm = {
                openOverlayPermissionSettings(context)
                // Note: We can't directly detect when permission is granted from settings
                // The calling code should handle this in onResume or similar lifecycle method
            },
            onCancel = {
                onPermissionDenied()
            }
        )
    }
}
