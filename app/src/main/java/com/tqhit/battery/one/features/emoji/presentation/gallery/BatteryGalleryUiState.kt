package com.tqhit.battery.one.features.emoji.presentation.gallery

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory

/**
 * Types of errors that can occur in the gallery
 */
enum class ErrorType {
    NETWORK_ERROR,          // No internet connection or network timeout
    REMOTE_CONFIG_ERROR,    // Firebase Remote Config fetch failed
    DATA_PARSING_ERROR,     // Error parsing JSON data from remote config
    PERMISSION_ERROR,       // Missing required permissions
    UNKNOWN_ERROR          // Unexpected error
}

/**
 * UI state for the Battery Gallery screen following modern MVI pattern.
 * Separates pure UI state from side effects for better state management.
 * 
 * This state class follows the established patterns from StatsChargeUiState:
 * - Immutable data class representing complete UI state
 * - Contains loading states, data, and error information
 * - Supports category filtering and search functionality
 * - Handles premium content and global toggle states
 * - Excludes navigation events (handled separately as effects)
 */
data class BatteryGalleryUiState(
    /**
     * Whether the gallery is currently loading data from remote config or local fallback
     */
    val isLoading: Boolean = false,
    
    /**
     * Whether the initial data load has completed (used to show shimmer only on first load)
     */
    val isInitialLoadComplete: Boolean = false,
    
    /**
     * Error message to display to the user, null if no error
     */
    val errorMessage: String? = null,

    /**
     * Type of error that occurred, null if no error
     */
    val errorType: ErrorType? = null,

    /**
     * Whether the error is recoverable (user can retry)
     */
    val isErrorRecoverable: Boolean = true,
    
    /**
     * Complete list of all available battery styles
     */
    val allStyles: List<BatteryStyle> = emptyList(),
    
    /**
     * Currently displayed battery styles (filtered by category/search)
     */
    val displayedStyles: List<BatteryStyle> = emptyList(),
    
    /**
     * Available categories for filtering
     */
    val categories: List<BatteryStyleCategory> = BatteryStyleCategory.getMainFilterCategories(),
    
    /**
     * Currently selected category for filtering
     */
    val selectedCategory: BatteryStyleCategory = BatteryStyleCategory.HOT,
    
    /**
     * Current search query, empty string if no search active
     */
    val searchQuery: String = "",
    
    /**
     * Whether search mode is active (search input is visible)
     */
    val isSearchActive: Boolean = false,
    
    /**
     * Whether the global emoji battery feature is enabled
     */
    val isGlobalToggleEnabled: Boolean = false,

    /**
     * Whether permissions should be requested (for backward compatibility)
     */
    val shouldRequestPermissions: Boolean = false,
    
    /**
     * Whether to show only premium styles
     */
    val showOnlyPremium: Boolean = false,
    
    /**
     * Whether to show only free styles
     */
    val showOnlyFree: Boolean = false,
    
    /**
     * Whether ads should be shown (based on user's ad-free purchase status)
     */
    val shouldShowAds: Boolean = true,
    
    /**
     * Whether the refresh operation is in progress (pull-to-refresh)
     */
    val isRefreshing: Boolean = false,
    
    /**
     * Last refresh timestamp for display purposes
     */
    val lastRefreshTimestamp: Long = 0L
) {
    
    /**
     * Checks if there are any styles to display
     */
    val hasStyles: Boolean
        get() = displayedStyles.isNotEmpty()
    
    /**
     * Checks if the gallery is in an empty state (no styles and not loading)
     */
    val isEmpty: Boolean
        get() = displayedStyles.isEmpty() && !isLoading
    
    /**
     * Checks if there's an error state
     */
    val hasError: Boolean
        get() = errorMessage != null
    
    /**
     * Gets the count of displayed styles for UI display
     */
    val displayedStylesCount: Int
        get() = displayedStyles.size
    
    /**
     * Gets the count of premium styles in the current display
     */
    val premiumStylesCount: Int
        get() = displayedStyles.count { it.isPremium }
    
    /**
     * Gets the count of free styles in the current display
     */
    val freeStylesCount: Int
        get() = displayedStyles.count { !it.isPremium }
    
    /**
     * Checks if the current category is HOT (popular styles)
     */
    val isHotCategorySelected: Boolean
        get() = selectedCategory == BatteryStyleCategory.HOT
    
    /**
     * Gets the display text for the current category
     */
    val selectedCategoryDisplayText: String
        get() = selectedCategory.getDisplayText()
    
    /**
     * Checks if any filters are active (search, premium only, free only)
     */
    val hasActiveFilters: Boolean
        get() = searchQuery.isNotBlank() || showOnlyPremium || showOnlyFree

    companion object {
        /**
         * Creates the initial state for the gallery
         */
        fun initial(): BatteryGalleryUiState {
            return BatteryGalleryUiState()
        }
        
        /**
         * Creates a loading state
         */
        fun loading(): BatteryGalleryUiState {
            return BatteryGalleryUiState(isLoading = true)
        }
        
        /**
         * Creates an error state with the given message and type
         */
        fun error(
            message: String,
            errorType: ErrorType = ErrorType.UNKNOWN_ERROR,
            isRecoverable: Boolean = true
        ): BatteryGalleryUiState {
            return BatteryGalleryUiState(
                isLoading = false,
                errorMessage = message,
                errorType = errorType,
                isErrorRecoverable = isRecoverable,
                isInitialLoadComplete = true
            )
        }

        /**
         * Creates a network error state with user-friendly message
         */
        fun networkError(): BatteryGalleryUiState {
            return error(
                message = "No internet connection. Please check your network and try again.",
                errorType = ErrorType.NETWORK_ERROR,
                isRecoverable = true
            )
        }

        /**
         * Creates a remote config error state with user-friendly message
         */
        fun remoteConfigError(): BatteryGalleryUiState {
            return error(
                message = "Unable to load latest content. Using offline data.",
                errorType = ErrorType.REMOTE_CONFIG_ERROR,
                isRecoverable = true
            )
        }

        /**
         * Creates a data parsing error state with user-friendly message
         */
        fun dataParsingError(): BatteryGalleryUiState {
            return error(
                message = "Content format error. Please try again later.",
                errorType = ErrorType.DATA_PARSING_ERROR,
                isRecoverable = true
            )
        }
        
        /**
         * Creates a success state with the given styles
         */
        fun success(
            allStyles: List<BatteryStyle>,
            selectedCategory: BatteryStyleCategory = BatteryStyleCategory.HOT
        ): BatteryGalleryUiState {
            val filteredStyles = when (selectedCategory) {
                BatteryStyleCategory.HOT -> allStyles.filter { it.isPopular }
                else -> allStyles.filter { it.category == selectedCategory }
            }
            
            return BatteryGalleryUiState(
                isLoading = false,
                isInitialLoadComplete = true,
                allStyles = allStyles,
                displayedStyles = filteredStyles,
                selectedCategory = selectedCategory,
                lastRefreshTimestamp = System.currentTimeMillis()
            )
        }
    }
}
