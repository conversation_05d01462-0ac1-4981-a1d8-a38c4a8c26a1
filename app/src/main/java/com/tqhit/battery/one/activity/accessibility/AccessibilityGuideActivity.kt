package com.tqhit.battery.one.activity.accessibility

import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import androidx.activity.OnBackPressedCallback
import com.tqhit.battery.one.R
import com.tqhit.battery.one.base.LocaleAwareActivity
import com.tqhit.battery.one.databinding.ActivityAccessibilityGuideBinding
import com.tqhit.battery.one.features.emoji.presentation.overlay.permission.EmojiOverlayPermissionManager
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.AndroidEntryPoint

/**
 * Activity that provides step-by-step visual guide for enabling accessibility service.
 * This is the second step in the enhanced accessibility permission flow.
 * 
 * Features:
 * - Visual step-by-step instructions with images
 * - Direct navigation to accessibility settings
 * - Automatic permission status checking on resume
 * - Proper back navigation handling
 * - Comprehensive logging for user interactions
 * - Theme-aware styling following app conventions
 */
@AndroidEntryPoint
class AccessibilityGuideActivity : LocaleAwareActivity<ActivityAccessibilityGuideBinding>() {

    companion object {
        private const val TAG = "AccessibilityGuideActivity"
        private const val ACCESSIBILITY_GUIDE_TAG = "ACCESSIBILITY_GUIDE"
    }

    override val binding by lazy { ActivityAccessibilityGuideBinding.inflate(layoutInflater) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        BatteryLogger.d(TAG, "Creating accessibility guide activity")
        BatteryLogger.d(ACCESSIBILITY_GUIDE_TAG, "ACCESSIBILITY_GUIDE_ACTIVITY_CREATED")
        
        try {
            setupActivity()
            setupActionBar()
            setupUI()
            setupListeners()
            
            BatteryLogger.d(ACCESSIBILITY_GUIDE_TAG, "ACCESSIBILITY_GUIDE_ACTIVITY_SETUP_COMPLETED")
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up accessibility guide activity", e)
            BatteryLogger.e(TAG, "Failed to setup accessibility guide activity: ${e.message}")
            finish()
        }
    }

    override fun onResume() {
        super.onResume()
        
        BatteryLogger.d(TAG, "Accessibility guide activity resumed, checking permission status")
        BatteryLogger.d(ACCESSIBILITY_GUIDE_TAG, "ACCESSIBILITY_GUIDE_ACTIVITY_RESUMED")
        
        // Check if accessibility permission is now granted
        if (EmojiOverlayPermissionManager.isAccessibilityServiceEnabled(this)) {
            BatteryLogger.d(ACCESSIBILITY_GUIDE_TAG, "ACCESSIBILITY_PERMISSION_GRANTED_ON_RESUME")
            Log.d(TAG, "Accessibility permission granted, finishing guide activity")
            
            // Permission granted, finish this activity and return to caller
            setResult(RESULT_OK)
            finish()
        } else {
            BatteryLogger.d(ACCESSIBILITY_GUIDE_TAG, "ACCESSIBILITY_PERMISSION_STILL_PENDING")
        }
    }

    /**
     * Sets up the activity with proper theme and content view
     */
    private fun setupActivity() {
        BatteryLogger.d(TAG, "Setting up accessibility guide activity")
        
        // Apply current theme
        applyCurrentTheme()
        
        // Set content view
        setContentView(binding.root)
    }

    /**
     * Sets up the action bar with back navigation
     */
    private fun setupActionBar() {
        BatteryLogger.d(TAG, "Setting up action bar for accessibility guide")
        
        try {
            setSupportActionBar(binding.toolbar)
            supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(true)
                setDisplayShowHomeEnabled(true)
                title = getString(R.string.accessibility_guide_title)
            }
            
            // Handle toolbar navigation click
            binding.toolbar.setNavigationOnClickListener {
                BatteryLogger.d(ACCESSIBILITY_GUIDE_TAG, "ACCESSIBILITY_GUIDE_BACK_NAVIGATION_CLICKED")
                handleBackNavigation()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up action bar", e)
            BatteryLogger.e(TAG, "Failed to setup action bar: ${e.message}")
        }
    }

    /**
     * Sets up the UI components and loads step images
     */
    override fun setupUI() {
        BatteryLogger.d(TAG, "Setting up accessibility guide UI")

        try {
            super.setupUI()
            // Set step instruction texts
            binding.step1Title.text = getString(R.string.accessibility_guide_step1)
            binding.step2Title.text = getString(R.string.accessibility_guide_step2)
            binding.step3Title.text = getString(R.string.accessibility_guide_step3)
            binding.goToSettingButton.text = getString(R.string.go_to_setting)
            
            // Load step images (using placeholder drawables for now)
            binding.step1Image.setImageResource(R.drawable.accessibility_step1)
            binding.step2Image.setImageResource(R.drawable.accessibility_step2)
            binding.step3Image.setImageResource(R.drawable.accessibility_step3)
            
            BatteryLogger.d(TAG, "Accessibility guide UI setup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up UI", e)
            BatteryLogger.e(TAG, "Failed to setup UI: ${e.message}")
        }
    }

    /**
     * Sets up event listeners for UI components
     */
    private fun setupListeners() {
        BatteryLogger.d(TAG, "Setting up accessibility guide listeners")
        
        try {
            // Go to Setting button click listener
            binding.goToSettingButton.setOnClickListener {
                BatteryLogger.d(ACCESSIBILITY_GUIDE_TAG, "ACCESSIBILITY_GUIDE_GO_TO_SETTING_CLICKED")
                openAccessibilitySettings()
            }
            
            // Handle back button press
            onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    BatteryLogger.d(ACCESSIBILITY_GUIDE_TAG, "ACCESSIBILITY_GUIDE_BACK_PRESSED")
                    handleBackNavigation()
                }
            })
            
            BatteryLogger.d(TAG, "Accessibility guide listeners setup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up listeners", e)
            BatteryLogger.e(TAG, "Failed to setup listeners: ${e.message}")
        }
    }

    /**
     * Opens Android accessibility settings
     */
    private fun openAccessibilitySettings() {
        BatteryLogger.d(TAG, "Opening accessibility settings")
        
        try {
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(intent)
            
            BatteryLogger.d(ACCESSIBILITY_GUIDE_TAG, "ACCESSIBILITY_SETTINGS_OPENED")
        } catch (e: Exception) {
            Log.e(TAG, "Error opening accessibility settings", e)
            BatteryLogger.e(TAG, "Failed to open accessibility settings: ${e.message}")
            
            // Fallback: try to open general settings
            try {
                val fallbackIntent = Intent(Settings.ACTION_SETTINGS)
                fallbackIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                startActivity(fallbackIntent)
                
                BatteryLogger.d(ACCESSIBILITY_GUIDE_TAG, "GENERAL_SETTINGS_OPENED_AS_FALLBACK")
            } catch (fallbackException: Exception) {
                Log.e(TAG, "Error opening fallback settings", fallbackException)
                BatteryLogger.e(TAG, "Failed to open fallback settings: ${fallbackException.message}")
            }
        }
    }

    /**
     * Handles back navigation (finish activity)
     */
    private fun handleBackNavigation() {
        BatteryLogger.d(TAG, "Handling back navigation for accessibility guide")
        
        try {
            setResult(RESULT_CANCELED)
            finish()
        } catch (e: Exception) {
            Log.e(TAG, "Error handling back navigation", e)
            BatteryLogger.e(TAG, "Failed to handle back navigation: ${e.message}")
        }
    }

    /**
     * Applies the current theme to the activity
     */
    private fun applyCurrentTheme() {
        try {
            // The theme is already applied through the manifest and LocaleAwareActivity
            // This method is here for consistency with existing activity patterns
            BatteryLogger.d(TAG, "Current theme applied to accessibility guide activity")
        } catch (e: Exception) {
            Log.e(TAG, "Error applying theme", e)
            BatteryLogger.e(TAG, "Failed to apply theme: ${e.message}")
        }
    }
}
