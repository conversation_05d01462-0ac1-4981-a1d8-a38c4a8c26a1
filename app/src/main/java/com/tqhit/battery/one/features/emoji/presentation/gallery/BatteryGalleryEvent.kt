package com.tqhit.battery.one.features.emoji.presentation.gallery

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory

/**
 * Events that can be triggered in the Battery Gallery screen.
 * Represents all user interactions and system events following MVI pattern.
 * 
 * This event class follows the established MVI patterns in the app:
 * - Sealed class hierarchy for type-safe event handling
 * - Covers all possible user interactions and system events
 * - Supports navigation, filtering, search, and premium unlock flows
 * - Integrates with existing ad and premium systems
 */
sealed class BatteryGalleryEvent {
    
    /**
     * Initial load event triggered when the fragment is created
     */
    object LoadInitialData : BatteryGalleryEvent()
    
    /**
     * Refresh event triggered by pull-to-refresh or manual refresh
     */
    object RefreshData : BatteryGalleryEvent()
    
    /**
     * Retry event triggered when user taps retry after an error
     */
    object RetryLoad : BatteryGalleryEvent()
    
    /**
     * Category selection event
     * @param category The selected category to filter by
     */
    data class SelectCategory(val category: BatteryStyleCategory) : BatteryGalleryEvent()
    
    /**
     * Search query change event
     * @param query The search query entered by the user
     */
    data class SearchQueryChanged(val query: String) : BatteryGalleryEvent()
    
    /**
     * Toggle search mode event (show/hide search input)
     */
    object ToggleSearchMode : BatteryGalleryEvent()
    
    /**
     * Clear search event (clear query and exit search mode)
     */
    object ClearSearch : BatteryGalleryEvent()
    
    /**
     * Battery style selection event
     * @param style The selected battery style
     */
    data class SelectBatteryStyle(val style: BatteryStyle) : BatteryGalleryEvent()
    
    /**
     * Global toggle change event (enable/disable emoji battery feature)
     * @param isEnabled Whether the feature should be enabled
     */
    data class ToggleGlobalFeature(val isEnabled: Boolean) : BatteryGalleryEvent()
    
    /**
     * Premium filter toggle event (show only premium styles)
     */
    object TogglePremiumFilter : BatteryGalleryEvent()
    
    /**
     * Free filter toggle event (show only free styles)
     */
    object ToggleFreeFilter : BatteryGalleryEvent()
    
    /**
     * Clear all filters event
     */
    object ClearAllFilters : BatteryGalleryEvent()
    
    /**
     * Premium unlock event for a specific style
     * @param style The premium style to unlock
     */
    data class UnlockPremiumStyle(val style: BatteryStyle) : BatteryGalleryEvent()
    
    /**
     * Watch ad to unlock premium style event
     * @param style The premium style to unlock via ad
     */
    data class WatchAdToUnlock(val style: BatteryStyle) : BatteryGalleryEvent()
    
    /**
     * Purchase premium unlock event
     * @param style The premium style to purchase
     */
    data class PurchasePremiumUnlock(val style: BatteryStyle) : BatteryGalleryEvent()
    
    /**
     * Ad loading events
     */
    sealed class AdEvent : BatteryGalleryEvent() {
        object LoadBannerAd : AdEvent()
        object LoadInterstitialAd : AdEvent()
        object ShowInterstitialAd : AdEvent()
        data class AdLoadFailed(val error: String) : AdEvent()
        object AdShown : AdEvent()
        object AdClosed : AdEvent()
    }
    
    /**
     * Navigation events
     */
    sealed class NavigationEvent : BatteryGalleryEvent() {
        data class NavigateToCustomize(val style: BatteryStyle) : NavigationEvent()
        object NavigateToSettings : NavigationEvent()
        object NavigateBack : NavigationEvent()
        object ShowPermissionDialog : NavigationEvent()
        data class ShowPremiumDialog(val style: BatteryStyle) : NavigationEvent()
    }
    
    /**
     * Error handling events
     */
    sealed class ErrorEvent : BatteryGalleryEvent() {
        data class ShowError(val message: String) : ErrorEvent()
        object DismissError : ErrorEvent()
        data class ShowToast(val message: String) : ErrorEvent()
    }
    
    /**
     * UI interaction events
     */
    sealed class UIEvent : BatteryGalleryEvent() {
        object ShowInfoDialog : UIEvent()
        object DismissInfoDialog : UIEvent()
        object ShowSortOptions : UIEvent()
        object DismissSortOptions : UIEvent()
        data class SortBy(val sortType: SortType) : UIEvent()
    }
    
    /**
     * Sort types for battery styles
     */
    enum class SortType {
        NAME_ASC,
        NAME_DESC,
        NEWEST_FIRST,
        OLDEST_FIRST,
        PREMIUM_FIRST,
        FREE_FIRST,
        POPULAR_FIRST
    }
    
    /**
     * System events
     */
    sealed class SystemEvent : BatteryGalleryEvent() {
        object OnResume : SystemEvent()
        object OnPause : SystemEvent()
        object OnDestroy : SystemEvent()
        data class OnPermissionResult(val granted: Boolean) : SystemEvent()
        data class OnActivityResult(val requestCode: Int, val resultCode: Int) : SystemEvent()
    }
    
    companion object {
        /**
         * Creates a category selection event
         */
        fun selectCategory(category: BatteryStyleCategory): BatteryGalleryEvent {
            return SelectCategory(category)
        }
        
        /**
         * Creates a style selection event
         */
        fun selectStyle(style: BatteryStyle): BatteryGalleryEvent {
            return SelectBatteryStyle(style)
        }
        
        /**
         * Creates a search query change event
         */
        fun searchQueryChanged(query: String): BatteryGalleryEvent {
            return SearchQueryChanged(query)
        }
        
        /**
         * Creates an error event
         */
        fun showError(message: String): BatteryGalleryEvent {
            return ErrorEvent.ShowError(message)
        }
        
        /**
         * Creates a navigation to customize event
         */
        fun navigateToCustomize(style: BatteryStyle): BatteryGalleryEvent {
            return NavigationEvent.NavigateToCustomize(style)
        }
    }
}
