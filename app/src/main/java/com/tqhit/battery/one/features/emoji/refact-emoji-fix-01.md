I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end. Make sure you fix all the linting, compilation or validation issues after successful implementation of the plan.

### Observations

After analyzing the emoji module codebase and the 4 reported issues, I found that the refactored MVI architecture is already in place with proper StateManager, EffectHandler, and service layers. The issues are specific bugs in data loading, image URL mapping, preview functionality, and premium logic rather than architectural problems. The Firebase Remote Config fallback data in `remote_config_defaults.xml` contains complete JSON for all categories including `hot_category` with 115+ items, so the data source is correct.

### Approach

The implementation will focus on targeted bug fixes across 4 specific areas: (1) Fix category ID mapping to ensure "HOT" tab loads from "hot_category", (2) Correct image URL field mapping in EmojiItem.toBatteryStyle() method, (3) Fix preview image loading to use selected items instead of hardcoded defaults, and (4) Resolve premium logic to work correctly when user has premium access. Each fix will be validated through logging and tested to ensure the existing MVI architecture continues to work properly.

### Reasoning

I explored the emoji module structure, examined the refactoring plan completion status, analyzed the Firebase Remote Config defaults containing category data, reviewed the EmojiCategoryService and EmojiItemService implementations, studied the BatteryGalleryViewModel data loading flow, investigated the EmojiItem to BatteryStyle conversion logic, and examined the EmojiCustomizeActivity preview loading mechanism to understand the root causes of the reported issues.

## Mermaid Diagram

sequenceDiagram
    participant U as User
    participant F as EmojiBatteryFragment  
    participant VM as BatteryGalleryViewModel
    participant SM as StateManager
    participant EIS as EmojiItemService
    participant RC as RemoteConfig/Defaults
    participant CA as CustomizeActivity
    participant CVM as CustomizeViewModel

    Note over U,CVM: Issue 1: HOT Tab Loading Fix
    U->>F: Select "HOT" tab
    F->>VM: handleEvent(SelectCategory.HOT)
    VM->>SM: selectCategory(HOT)
    Note over SM: Fix: Ensure HOT maps to "hot_category"
    SM->>VM: Updated state with HOT selected
    VM->>EIS: getEmojiItemsByCategory("hot_category")
    EIS->>RC: getString("hot_category")
    RC-->>EIS: JSON with 115+ items
    Note over EIS: Fix: Validate JSON parsing & filtering
    EIS-->>VM: List of valid EmojiItems
    Note over VM: Fix: Convert EmojiItem to BatteryStyle correctly
    VM->>SM: updateWithStyles(batteryStyles)
    SM-->>VM: State with displayed styles
    VM-->>F: Updated UI state with HOT items

    Note over U,CVM: Issue 2: Image URL Mapping Fix
    Note over VM: Fix: EmojiItem.toBatteryStyle() mapping
    Note over VM: - batteryImageUrl: custom_fields.battery
    Note over VM: - emojiImageUrl: custom_fields.emoji
    Note over VM: - Add fallback logic for missing URLs

    Note over U,CVM: Issue 3: Live Preview Fix
    U->>F: Select battery style
    F->>VM: handleEvent(SelectBatteryStyle)
    VM->>CA: Navigate to customize with style
    CA->>CVM: Initialize with selected style
    CVM-->>CA: UI state with selected styles
    Note over CA: Fix: loadCompositePreviewImages()
    Note over CA: - Use selectedBatteryStyle.batteryImageUrl
    Note over CA: - Use selectedEmojiStyle.emojiImageUrl
    Note over CA: - Remove hardcoded defaults
    CA->>CA: Load actual preview images

    Note over U,CVM: Issue 4: Premium Logic Fix
    U->>CA: Click Apply button
    CA->>CVM: handleEvent(ApplyCustomization)
    Note over CVM: Fix: Premium validation logic
    Note over CVM: - Check both battery & emoji premium status
    Note over CVM: - Allow if user has premium entitlement
    Note over CVM: - Block only if premium needed & user lacks it
    alt User has premium OR no premium content
        CVM->>CVM: Proceed with customization
        CVM-->>CA: Success effect
    else Premium content & no premium
        CVM-->>CA: ShowPremiumDialog effect
    end

## Proposed File Changes

### app/src/main/java/com/tqhit/battery/one/features/emoji/domain/model/BatteryStyleCategory.kt(MODIFY)

References: 

- app/src/main/res/xml/remote_config_defaults.xml

Fix the `toCategoryId()` extension function to ensure proper mapping between UI categories and remote config keys. Verify that:

- `BatteryStyleCategory.HOT` maps to `"hot_category"`
- `BatteryStyleCategory.HEART` maps to `"heart_category"`
- `BatteryStyleCategory.CUTE` maps to `"cute_category"`
- `BatteryStyleCategory.STICKER_3D` maps to `"sticker3d_category"`
- `BatteryStyleCategory.EMOTION` maps to `"emotion_category"`
- `BatteryStyleCategory.ANIMAL` maps to `"animal_category"`
- `BatteryStyleCategory.FOOD` maps to `"food_category"`
- `BatteryStyleCategory.OTHER` maps to `"other_category"`

Add comprehensive logging to track category mapping and ensure the mapping matches the keys defined in `/home/<USER>/AndroidStudioProjects/TJ_BatteryOne/app/src/main/res/xml/remote_config_defaults.xml`. Also add a reverse mapping function `fromCategoryId()` to ensure bidirectional consistency.

### app/src/main/java/com/tqhit/battery/one/features/emoji/domain/model/BatteryStyle.kt(MODIFY)

References: 

- app/src/main/res/xml/remote_config_defaults.xml

Fix the `EmojiItem.toBatteryStyle()` method to ensure proper image URL mapping and add fallback logic for missing custom fields:

1. **Strengthen null safety**: Ensure `customFields` null handling works correctly and provides meaningful fallbacks
2. **Add URL validation**: Validate that URLs are not empty strings before assignment
3. **Improve fallback logic**: If `custom_fields.battery` or `custom_fields.emoji` are null/empty, use `photo` as fallback for both battery and emoji images
4. **Add comprehensive logging**: Log all image URLs during conversion to help debug wrong image issues
5. **Fix `isValid()` method**: Ensure it doesn't reject items with valid data but missing optional fields

The method should prioritize:
- `batteryImageUrl`: custom_fields.battery → photo → thumbnail (in that order)
- `emojiImageUrl`: custom_fields.emoji → photo → thumbnail (in that order)

Add detailed logging with "IMAGE_URL_MAPPING" prefix to track which URLs are being used for each field.

### app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/gallery/BatteryGalleryStateManager.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/gallery/BatteryGalleryViewModel.kt

Fix the `updateWithStyles()` method to ensure emoji items loaded from remote config are properly displayed:

1. **Debug empty lists**: Add logging to track when `updateWithStyles()` receives a non-empty list but results in empty `displayedStyles`
2. **Fix filtering logic**: Ensure that styles loaded from emoji services are not incorrectly filtered out by search queries or category filters
3. **Preserve emoji items**: When updating with emoji items from remote config, ensure they are not lost during state transformations
4. **Add state validation**: Validate that the new state contains the expected number of styles after update

Add comprehensive logging with "STATE_TRANSFORM" prefix to track:
- Input styles count and details
- Applied filters and their effects
- Final displayed styles count
- Any styles that were filtered out and why

### app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/EmojiCustomizeActivity.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/CustomizeUiState.kt

Fix the `loadCompositePreviewImages()` method to use selected items instead of hardcoded defaults:

1. **Fix image URL selection**: Ensure the method uses the correct image URLs from the selected battery and emoji styles:
   - For battery preview: use `state.selectedBatteryStyle.batteryImageUrl` with fallback to `customizePreviewUrl`
   - For emoji preview: use `state.selectedEmojiStyle.emojiImageUrl` with fallback to `customizePreviewUrl`

2. **Add URL validation**: Check if URLs are not empty before loading, and provide meaningful fallbacks

3. **Improve error handling**: Add better error handling for failed image loads with retry logic

4. **Add state debugging**: Log the complete state information to understand why preview might show defaults:
   - Log selected battery style details (ID, name, all image URLs)
   - Log selected emoji style details (ID, name, all image URLs)
   - Log which URLs are actually being used for loading

5. **Fix placeholder logic**: Ensure placeholders are only used when no valid style is selected, not when URLs are temporarily empty

Add "PREVIEW_DEBUG" logging to track the complete image loading flow and identify where hardcoded defaults are being used instead of selected items.

### app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/CustomizeViewModel.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/CustomizeEffectHandler.kt
- app/src/main/java/com/tqhit/battery/one/repository/AppRepository.kt

Fix the premium logic in the `handleEvent()` method for `ApplyCustomization` event to work correctly when user has premium access:

1. **Fix premium validation logic**: Ensure the premium check correctly handles the case where user has premium entitlement:
   - Check if either battery style OR emoji style is premium
   - If user has premium entitlement, allow the operation to proceed
   - Only block if premium content is selected AND user doesn't have premium

2. **Fix apply button state**: Ensure `canApplyChanges` in the UI state correctly reflects premium status:
   - Should be `true` when user has premium OR when no premium content is selected
   - Should be `false` only when premium content is selected AND user lacks premium

3. **Add premium debugging**: Add comprehensive logging to track:
   - Premium status of selected battery and emoji styles
   - User's premium entitlement status
   - Final decision on whether to allow or block the operation

4. **Fix effect emission**: Ensure the correct effect is emitted based on premium validation:
   - Emit `ShowPremiumDialog` only when premium content is selected and user lacks premium
   - Emit `NavigateToCustomize` or `SaveCustomization` when operation should proceed

Add "PREMIUM_LOGIC" logging prefix to track the complete premium validation flow and identify where the logic fails.

### app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/CustomizeStateManager.kt(MODIFY)

References: 

- app/src/main/java/com/tqhit/battery/one/features/emoji/presentation/customize/CustomizeUiState.kt

Fix the `canApplyChanges` calculation in state transformation methods to properly handle premium logic:

1. **Fix premium state calculation**: Update methods that calculate `canApplyChanges` to properly consider premium status:
   - `updateWithBatteryStyle()`: Check if new battery style affects premium requirements
   - `updateWithEmojiStyle()`: Check if new emoji style affects premium requirements
   - `updatePremiumStatus()`: Recalculate apply button state when premium status changes

2. **Add premium validation helper**: Create a helper method to determine if current selection requires premium:
   - Check both selected battery and emoji styles for premium flags
   - Consider user's premium entitlement status
   - Return whether the current selection can be applied

3. **Fix state consistency**: Ensure all state updates maintain consistency between:
   - Selected styles (battery and emoji)
   - Premium requirements of selected styles
   - User's premium entitlement
   - Final `canApplyChanges` flag

4. **Add validation logging**: Log state transformations that affect premium logic to help debug apply button issues

Add "STATE_PREMIUM" logging to track how premium status affects state calculations and the apply button state.

### app/src/main/java/com/tqhit/battery/one/features/emoji/data/service/EmojiItemService.kt(MODIFY)

References: 

- app/src/main/res/xml/remote_config_defaults.xml

Add enhanced debugging and validation to the `getEmojiItemsByCategory()` method to help diagnose data loading issues:

1. **Add category validation**: Validate that the requested categoryId exists in the available categories list before attempting to fetch

2. **Enhance JSON debugging**: Add more detailed logging about the JSON data retrieved:
   - Log first 200 characters of JSON for debugging
   - Log the number of items parsed vs. number of valid items after filtering
   - Log specific reasons why items are filtered out

3. **Add URL validation**: In `filterAndSortItems()`, add validation for image URLs:
   - Check that thumbnail, photo, and custom_fields URLs are valid HTTP/HTTPS URLs
   - Log items with missing or invalid URLs
   - Consider items with missing custom_fields as valid (they'll use fallbacks)

4. **Improve error handling**: Add specific error handling for different failure scenarios:
   - Network/connectivity issues
   - JSON parsing errors
   - Empty or malformed data

5. **Add performance logging**: Log timing information for data fetching and parsing to identify performance bottlenecks

Add "EMOJI_SERVICE_DEBUG" logging prefix to track the complete data loading pipeline and identify where data is lost or corrupted.