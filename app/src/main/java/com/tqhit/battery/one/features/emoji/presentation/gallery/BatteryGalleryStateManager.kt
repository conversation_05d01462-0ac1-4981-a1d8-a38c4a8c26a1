package com.tqhit.battery.one.features.emoji.presentation.gallery

import android.util.Log
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import javax.inject.Inject
import javax.inject.Singleton

/**
 * State manager for the Battery Gallery screen that handles pure state transformations.
 * This class contains all business logic for filtering, searching, and state updates
 * using pure functions that take current state and return new state.
 * 
 * This follows the reducer pattern and makes state changes predictable and testable.
 * All methods are pure functions with no side effects.
 */
@Singleton
class BatteryGalleryStateManager @Inject constructor() {
    
    companion object {
        private const val TAG = "BatteryGalleryStateMgr"
    }
    
    /**
     * Updates state when a category is selected
     */
    fun selectCategory(
        currentState: BatteryGalleryUiState,
        category: BatteryStyleCategory
    ): BatteryGalleryUiState {
        Log.d(TAG, "STATE_TRANSFORM: Selecting category: ${category.displayName}")
        
        val filteredStyles = filterStylesByCategory(currentState.allStyles, category)
        val finalStyles = applyAdditionalFilters(filteredStyles, currentState.copy(selectedCategory = category))
        
        return currentState.copy(
            selectedCategory = category,
            displayedStyles = finalStyles,
            searchQuery = "", // Clear search when changing category
            isSearchActive = false
        )
    }
    
    /**
     * Updates state when search query changes
     */
    fun updateSearchQuery(
        currentState: BatteryGalleryUiState,
        query: String
    ): BatteryGalleryUiState {
        Log.d(TAG, "STATE_TRANSFORM: Updating search query: $query")
        
        val filteredStyles = if (query.isBlank()) {
            filterStylesByCategory(currentState.allStyles, currentState.selectedCategory)
        } else {
            filterStyles(currentState.allStyles, currentState.selectedCategory, query)
        }
        
        val finalStyles = applyAdditionalFilters(filteredStyles, currentState)
        
        return currentState.copy(
            searchQuery = query,
            displayedStyles = finalStyles
        )
    }
    
    /**
     * Toggles search mode
     */
    fun toggleSearchMode(currentState: BatteryGalleryUiState): BatteryGalleryUiState {
        Log.d(TAG, "STATE_TRANSFORM: Toggling search mode")
        
        val newSearchActive = !currentState.isSearchActive
        val newQuery = if (newSearchActive) currentState.searchQuery else ""
        
        val filteredStyles = if (newQuery.isBlank()) {
            filterStylesByCategory(currentState.allStyles, currentState.selectedCategory)
        } else {
            filterStyles(currentState.allStyles, currentState.selectedCategory, newQuery)
        }
        
        val finalStyles = applyAdditionalFilters(filteredStyles, currentState)
        
        return currentState.copy(
            isSearchActive = newSearchActive,
            searchQuery = newQuery,
            displayedStyles = finalStyles
        )
    }
    
    /**
     * Clears search and exits search mode
     */
    fun clearSearch(currentState: BatteryGalleryUiState): BatteryGalleryUiState {
        Log.d(TAG, "STATE_TRANSFORM: Clearing search")
        
        val filteredStyles = filterStylesByCategory(currentState.allStyles, currentState.selectedCategory)
        val finalStyles = applyAdditionalFilters(filteredStyles, currentState)
        
        return currentState.copy(
            searchQuery = "",
            isSearchActive = false,
            displayedStyles = finalStyles
        )
    }
    
    /**
     * Toggles premium filter
     */
    fun togglePremiumFilter(currentState: BatteryGalleryUiState): BatteryGalleryUiState {
        Log.d(TAG, "STATE_TRANSFORM: Toggling premium filter")
        
        val newShowOnlyPremium = !currentState.showOnlyPremium
        val filteredStyles = filterStyles(currentState.allStyles, currentState.selectedCategory, currentState.searchQuery)
        val newState = currentState.copy(
            showOnlyPremium = newShowOnlyPremium,
            showOnlyFree = if (newShowOnlyPremium) false else currentState.showOnlyFree
        )
        val finalStyles = applyAdditionalFilters(filteredStyles, newState)
        
        return newState.copy(displayedStyles = finalStyles)
    }
    
    /**
     * Toggles free filter
     */
    fun toggleFreeFilter(currentState: BatteryGalleryUiState): BatteryGalleryUiState {
        Log.d(TAG, "STATE_TRANSFORM: Toggling free filter")
        
        val newShowOnlyFree = !currentState.showOnlyFree
        val filteredStyles = filterStyles(currentState.allStyles, currentState.selectedCategory, currentState.searchQuery)
        val newState = currentState.copy(
            showOnlyFree = newShowOnlyFree,
            showOnlyPremium = if (newShowOnlyFree) false else currentState.showOnlyPremium
        )
        val finalStyles = applyAdditionalFilters(filteredStyles, newState)
        
        return newState.copy(displayedStyles = finalStyles)
    }
    
    /**
     * Clears all filters
     */
    fun clearAllFilters(currentState: BatteryGalleryUiState): BatteryGalleryUiState {
        Log.d(TAG, "STATE_TRANSFORM: Clearing all filters")
        
        val filteredStyles = filterStylesByCategory(currentState.allStyles, currentState.selectedCategory)
        
        return currentState.copy(
            searchQuery = "",
            isSearchActive = false,
            showOnlyPremium = false,
            showOnlyFree = false,
            displayedStyles = filteredStyles
        )
    }
    
    /**
     * Updates state with new styles data
     */
    fun updateWithStyles(
        currentState: BatteryGalleryUiState,
        allStyles: List<BatteryStyle>
    ): BatteryGalleryUiState {
        Log.d(TAG, "STATE_TRANSFORM: Updating with ${allStyles.size} styles")

        // Debug empty lists - track when updateWithStyles receives non-empty list but results in empty displayedStyles
        if (allStyles.isNotEmpty()) {
            Log.d(TAG, "STATE_TRANSFORM: Input styles details:")
            allStyles.forEachIndexed { index, style ->
                Log.d(TAG, "STATE_TRANSFORM:   [$index] ${style.name} (id=${style.id}, category=${style.category.displayName}, isPremium=${style.isPremium}, isPopular=${style.isPopular})")
            }
        }

        val filteredStyles = filterStyles(allStyles, currentState.selectedCategory, currentState.searchQuery)
        val finalStyles = applyAdditionalFilters(filteredStyles, currentState)

        // Add state validation - validate that the new state contains the expected number of styles after update
        Log.d(TAG, "STATE_TRANSFORM: Filtering results:")
        Log.d(TAG, "STATE_TRANSFORM:   - Input styles: ${allStyles.size}")
        Log.d(TAG, "STATE_TRANSFORM:   - After category/search filter: ${filteredStyles.size}")
        Log.d(TAG, "STATE_TRANSFORM:   - After additional filters: ${finalStyles.size}")
        Log.d(TAG, "STATE_TRANSFORM:   - Selected category: ${currentState.selectedCategory.displayName}")
        Log.d(TAG, "STATE_TRANSFORM:   - Search query: '${currentState.searchQuery}'")
        Log.d(TAG, "STATE_TRANSFORM:   - Show only premium: ${currentState.showOnlyPremium}")
        Log.d(TAG, "STATE_TRANSFORM:   - Show only free: ${currentState.showOnlyFree}")

        // Log any styles that were filtered out and why
        if (allStyles.size > finalStyles.size) {
            val filteredOutStyles = allStyles - finalStyles.toSet()
            Log.w(TAG, "STATE_TRANSFORM: ${filteredOutStyles.size} styles were filtered out:")
            filteredOutStyles.forEach { style ->
                val reasons = mutableListOf<String>()
                if (!filterStylesByCategory(listOf(style), currentState.selectedCategory).contains(style)) {
                    reasons.add("category mismatch")
                }
                if (currentState.searchQuery.isNotBlank() && !style.matchesSearch(currentState.searchQuery)) {
                    reasons.add("search query mismatch")
                }
                if (currentState.showOnlyPremium && !style.isPremium) {
                    reasons.add("not premium")
                }
                if (currentState.showOnlyFree && style.isPremium) {
                    reasons.add("not free")
                }
                Log.w(TAG, "STATE_TRANSFORM:   - ${style.name} (${reasons.joinToString(", ")})")
            }
        }

        return currentState.copy(
            isLoading = false,
            isInitialLoadComplete = true,
            allStyles = allStyles,
            displayedStyles = finalStyles,
            errorMessage = null,
            errorType = null,
            lastRefreshTimestamp = System.currentTimeMillis()
        )
    }
    
    /**
     * Updates state with loading status
     */
    fun updateLoading(
        currentState: BatteryGalleryUiState,
        isLoading: Boolean
    ): BatteryGalleryUiState {
        return currentState.copy(isLoading = isLoading)
    }
    
    /**
     * Updates state with error
     */
    fun updateError(
        currentState: BatteryGalleryUiState,
        errorMessage: String,
        errorType: ErrorType = ErrorType.UNKNOWN_ERROR,
        isRecoverable: Boolean = true
    ): BatteryGalleryUiState {
        Log.e(TAG, "STATE_TRANSFORM: Setting error state: $errorMessage")
        
        return currentState.copy(
            isLoading = false,
            errorMessage = errorMessage,
            errorType = errorType,
            isErrorRecoverable = isRecoverable,
            isInitialLoadComplete = true
        )
    }
    
    /**
     * Updates global toggle state
     */
    fun updateGlobalToggle(
        currentState: BatteryGalleryUiState,
        isEnabled: Boolean
    ): BatteryGalleryUiState {
        return currentState.copy(isGlobalToggleEnabled = isEnabled)
    }
    
    /**
     * Updates refresh state
     */
    fun updateRefreshing(
        currentState: BatteryGalleryUiState,
        isRefreshing: Boolean
    ): BatteryGalleryUiState {
        return currentState.copy(isRefreshing = isRefreshing)
    }
    
    // Private helper methods for filtering logic
    
    /**
     * Filters styles by category
     */
    private fun filterStylesByCategory(
        styles: List<BatteryStyle>,
        category: BatteryStyleCategory
    ): List<BatteryStyle> {
        Log.d(TAG, "FILTERING: Filtering ${styles.size} styles by category: ${category.displayName}")
        Log.i(TAG, "EMOJI_LOADING_FLOW: ========== CATEGORY FILTERING ==========")
        Log.i(TAG, "EMOJI_LOADING_FLOW: Target category: ${category.displayName}")
        Log.i(TAG, "EMOJI_LOADING_FLOW: Input styles count: ${styles.size}")

        // Log all input styles and their categories for debugging
        styles.forEachIndexed { index, style ->
            Log.d(TAG, "EMOJI_LOADING_FLOW: Style[$index]: '${style.name}' -> category: ${style.category.displayName}")
        }

        val filtered = when (category) {
            BatteryStyleCategory.HOT -> {
                // Fix filtering logic: HOT category should show items that match the HOT category
                // instead of filtering by isPopular flag (which is false for emoji items from remote config)
                val hotStyles = styles.filter { it.category == BatteryStyleCategory.HOT }
                Log.d(TAG, "FILTERING: HOT category - found ${hotStyles.size} HOT category styles")
                Log.i(TAG, "EMOJI_LOADING_FLOW: HOT category direct match: ${hotStyles.size} styles")

                // If no styles have HOT category, fall back to popular styles for backward compatibility
                if (hotStyles.isEmpty()) {
                    val popularStyles = styles.filter { it.isPopular }
                    Log.d(TAG, "FILTERING: HOT category fallback - found ${popularStyles.size} popular styles")
                    Log.i(TAG, "EMOJI_LOADING_FLOW: HOT category fallback to popular: ${popularStyles.size} styles")
                    popularStyles
                } else {
                    hotStyles
                }
            }
            else -> {
                val categoryStyles = styles.filter { it.category == category }
                Log.d(TAG, "FILTERING: Category $category - found ${categoryStyles.size} matching styles")
                Log.i(TAG, "EMOJI_LOADING_FLOW: Category ${category.displayName} direct match: ${categoryStyles.size} styles")

                // Enhanced debugging for category mismatches
                if (categoryStyles.isEmpty() && styles.isNotEmpty()) {
                    Log.w(TAG, "EMOJI_LOADING_FLOW: WARNING - No styles match category ${category.displayName}")
                    Log.w(TAG, "EMOJI_LOADING_FLOW: Available categories in styles:")
                    styles.map { it.category }.distinct().forEach { availableCategory ->
                        val count = styles.count { it.category == availableCategory }
                        Log.w(TAG, "EMOJI_LOADING_FLOW:   - ${availableCategory.displayName}: $count styles")
                    }
                }

                categoryStyles
            }
        }

        Log.i(TAG, "EMOJI_LOADING_FLOW: Filtered result: ${filtered.size} styles")
        Log.i(TAG, "EMOJI_LOADING_FLOW: ========== END CATEGORY FILTERING ==========")
        return filtered
    }
    
    /**
     * Filters styles by category and search query
     */
    private fun filterStyles(
        styles: List<BatteryStyle>,
        category: BatteryStyleCategory,
        searchQuery: String
    ): List<BatteryStyle> {
        val categoryFiltered = filterStylesByCategory(styles, category)
        return if (searchQuery.isNotBlank()) {
            categoryFiltered.filter { it.matchesSearch(searchQuery) }
        } else {
            categoryFiltered
        }
    }
    
    /**
     * Applies additional filters (premium/free)
     */
    private fun applyAdditionalFilters(
        styles: List<BatteryStyle>,
        state: BatteryGalleryUiState
    ): List<BatteryStyle> {
        return when {
            state.showOnlyPremium -> styles.filter { it.isPremium }
            state.showOnlyFree -> styles.filter { !it.isPremium }
            else -> styles
        }
    }
}
