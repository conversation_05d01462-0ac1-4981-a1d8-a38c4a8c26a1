package com.tqhit.battery.one.activity.starting

import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.LinearLayout
import androidx.viewbinding.ViewBinding
import androidx.viewpager.widget.PagerAdapter
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.applovin.mediation.nativeAds.MaxNativeAdViewBinder
import com.facebook.shimmer.Shimmer
import com.facebook.shimmer.ShimmerFrameLayout
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.R
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager

class StartingViewAdapter(private val views: ArrayList<ViewBinding>,
                          private val applovinNativeAdManager: ApplovinNativeAdManager,
                          private val remoteConfigHelper: FirebaseRemoteConfigHelper

                          ) : PagerAdapter() {
    private val loadedAds = mutableSetOf<Int>()


    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val view = views[position]
        container.addView(view.root)

        val nativeAdContainer = view.root.findViewById<ShimmerFrameLayout>(R.id.nativeAd)

        if (!loadedAds.contains(position) && nativeAdContainer != null) {
            Log.d("NativeAd", "Load ad for page $position")

            setupNativeAd(position, nativeAdContainer)
        }

//        setupNextButton(view)

        return view.root
    }


    fun setupNextButton(viewBinding: ViewBinding) {
        val delay = remoteConfigHelper.getLong("onboarding_next_delay")
        val nextPageBtn = viewBinding.root.findViewById<LinearLayout>(R.id.next_page)

        nextPageBtn.let {
            it.isEnabled = false
            it.alpha = 0.4f

            Handler(Looper.getMainLooper()).postDelayed({
                it.isEnabled = true
                it.alpha = 1.0f
            }, delay)
        }
    }



    override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
        container.removeView(`object` as View)
    }

    override fun getCount(): Int = views.size

    override fun isViewFromObject(view: View, `object`: Any): Boolean = view == `object`


    fun getViewAt(position: Int): ViewBinding? {
        return views.getOrNull(position)
    }

    private fun setupNativeAd(position: Int, container: ShimmerFrameLayout){

        Handler(Looper.getMainLooper()).postDelayed({
            try {
                applovinNativeAdManager.loadNativeAd(
                    onAdLoaded = {
                        val maxNativeAdView = createNativeAdView(container)

                        applovinNativeAdManager.removeCachedNativeAd(it)
                        applovinNativeAdManager.render(it, maxNativeAdView)
                        container.removeAllViews()
                        container.hideShimmer()
                        container.addView(maxNativeAdView)

                        loadedAds.add(position)

                        Log.d("NativeAd", "Loaded ad for page $position")
                    },
                    onAdLoadFailed = { errorMsg ->
                        Log.e("NativeAd", "Failed to load ad for page $position: $errorMsg")
                        container.hideShimmer()
                    }
                )
            } catch (e: Exception) {
                Log.e("NativeAd", "Exception loading ad for page $position", e)
                container.hideShimmer()
            }
        }, 3000) // 3 second delay to allow MAX SDK initialization
    }

    private fun createNativeAdView(container: ViewGroup): MaxNativeAdView
    {
        val binder: MaxNativeAdViewBinder =
            MaxNativeAdViewBinder.Builder(R.layout.layout_native_ads)
                .setTitleTextViewId(R.id.title_text_view)
                .setBodyTextViewId(R.id.body_text_view)
                .setStarRatingContentViewGroupId(R.id.star_rating_view )
                .setAdvertiserTextViewId(R.id.advertiser_text_view)
                .setIconImageViewId(R.id.icon_image_view)
                .setMediaContentViewGroupId(R.id.media_view_container)
                .setOptionsContentViewGroupId(R.id.ad_options_view)
                .setCallToActionButtonId(R.id.cta_button)
                .build()
        return MaxNativeAdView(binder, container.context)
    }

}