package com.tqhit.battery.one.features.emoji.di

import com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryStateManager
import com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEffectHandler
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeStateManager
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEffectHandler
import com.tqhit.battery.one.features.emoji.presentation.overlay.permission.EmojiAccessibilityPermissionTrackerService
import com.tqhit.battery.one.features.emoji.presentation.overlay.permission.EmojiUnifiedToggleManager
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing dependencies for the emoji battery feature.
 *
 * This module follows the stats module architecture pattern and provides
 * concrete bindings for the emoji battery feature components.
 *
 * Architecture Integration:
 * - Follows established stats module DI patterns
 * - Integrates with CoreBatteryStatsService for battery data
 * - Supports clean architecture separation (Data, Domain, Presentation layers)
 * - Uses Singleton scope for shared dependencies
 * - Uses Firebase Remote Config with local JSON fallback
 * - Provides new MVI architecture components (StateManager, EffectHandler)
 *
 * Current Bindings:
 * - CustomizationRepository interface binding ✅
 * - BatteryGalleryStateManager for gallery state management ✅
 * - BatteryGalleryEffectHandler for gallery side effects ✅
 * - CustomizeStateManager for customize state management ✅
 * - CustomizeEffectHandler for customize side effects ✅
 * - EmojiCategoryService and EmojiItemService (provided via constructor injection)
 * - EmojiAccessibilityPermissionTrackerService for permission tracking ✅
 * - EmojiUnifiedToggleManager for unified toggle management ✅
 *
 * Future Bindings (to be added in later phases):
 * - Cache implementations for data persistence
 * - Overlay service bindings
 *
 * @see com.tqhit.battery.one.features.stats.charge.di.StatsChargeDIModule
 * @see com.tqhit.battery.one.features.stats.health.di.HealthDIModule
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class EmojiBatteryDIModule {

    companion object {

        /**
         * Provides BatteryGalleryStateManager for gallery state management.
         * Uses @Provides instead of @Binds since it's a concrete class with no interface.
         *
         * @return BatteryGalleryStateManager instance
         */
        @Provides
        @Singleton
        fun provideBatteryGalleryStateManager(): BatteryGalleryStateManager {
            return BatteryGalleryStateManager()
        }

        /**
         * Provides CustomizeStateManager for customize state management.
         * Uses @Provides instead of @Binds since it's a concrete class with no interface.
         *
         * @return CustomizeStateManager instance
         */
        @Provides
        @Singleton
        fun provideCustomizeStateManager(): CustomizeStateManager {
            return CustomizeStateManager()
        }

        /**
         * Provides EmojiAccessibilityPermissionTrackerService for emoji permission tracking.
         * Uses @Provides instead of @Binds since it's a concrete class with no interface.
         *
         * @return EmojiAccessibilityPermissionTrackerService instance
         */
        @Provides
        @Singleton
        fun provideEmojiAccessibilityPermissionTrackerService(): EmojiAccessibilityPermissionTrackerService {
            return EmojiAccessibilityPermissionTrackerService()
        }

        /**
         * Provides EmojiUnifiedToggleManager for unified toggle management.
         * Uses @Provides instead of @Binds since it's a concrete class with no interface.
         *
         * @return EmojiUnifiedToggleManager instance
         */
        @Provides
        @Singleton
        fun provideEmojiUnifiedToggleManager(
            emojiAccessibilityServiceManager: com.tqhit.battery.one.features.emoji.presentation.overlay.EmojiAccessibilityServiceManager,
            permissionTrackerService: EmojiAccessibilityPermissionTrackerService
        ): EmojiUnifiedToggleManager {
            return EmojiUnifiedToggleManager(emojiAccessibilityServiceManager, permissionTrackerService)
        }
    }

    /**
     * Binds the CustomizationRepository interface to CustomizationRepositoryImpl.
     * This ensures that whenever CustomizationRepository is injected,
     * the CustomizationRepositoryImpl implementation will be provided.
     *
     * The implementation uses Jetpack DataStore for modern, type-safe persistence
     * that integrates well with the existing app architecture.
     *
     * @param customizationRepositoryImpl The implementation with DataStore integration
     * @return The CustomizationRepository interface
     */
    @Binds
    @Singleton
    abstract fun bindCustomizationRepository(
        customizationRepositoryImpl: CustomizationRepositoryImpl
    ): CustomizationRepository

    // Note: BatteryGalleryEffectHandler and CustomizeEffectHandler are concrete classes
    // with @Inject constructors, so they don't need explicit bindings.
    // Hilt will automatically provide them when requested as dependencies.
}
