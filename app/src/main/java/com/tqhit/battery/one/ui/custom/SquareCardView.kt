package com.tqhit.battery.one.ui.custom

import android.content.Context
import android.util.AttributeSet
import androidx.cardview.widget.CardView

/**
 * A CardView that maintains a 1:1 aspect ratio (square)
 * Used for emoji gallery items to ensure consistent square appearance
 */
class SquareCardView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : CardView(context, attrs, defStyleAttr) {

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        // Get the width measurement
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        
        // Make height equal to width to create a square
        val width = measuredWidth
        setMeasuredDimension(width, width)
    }
}
