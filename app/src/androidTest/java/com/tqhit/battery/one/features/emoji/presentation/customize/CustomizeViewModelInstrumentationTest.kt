package com.tqhit.battery.one.features.emoji.presentation.customize

import android.content.Context
import android.content.Intent
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.SmallTest
import com.tqhit.battery.one.features.emoji.data.service.EmojiItemService
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase
import com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase
import com.tqhit.battery.one.features.emoji.presentation.overlay.EmojiBatteryAccessibilityService
import com.tqhit.battery.one.features.emoji.presentation.overlay.permission.EmojiOverlayPermissionManager
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.android.testing.BindValue
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import dagger.hilt.android.testing.HiltTestApplication
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.spyk
import io.mockk.verify
import io.mockk.any
import io.mockk.match
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import javax.inject.Inject

@SmallTest
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
@Config(application = HiltTestApplication::class)
@ExperimentalCoroutinesApi
class CustomizeViewModelInstrumentationTest {

    @get:Rule
    var hiltRule = HiltAndroidRule(this)

    @BindValue
    lateinit var mockLoadCustomizationUseCase: LoadCustomizationUseCase

    @BindValue
    lateinit var mockSaveCustomizationUseCase: SaveCustomizationUseCase

    @BindValue
    lateinit var mockEmojiItemService: EmojiItemService

    @BindValue
    lateinit var mockCustomizationRepository: CustomizationRepository

    @ApplicationContext
    @Inject
    lateinit var context: Context

    private lateinit var viewModel: CustomizeViewModel

    private val customizationConfigFlow = MutableStateFlow(CustomizationConfig.createDefault())

    @Before
    fun setUp() {
        hiltRule.inject()

        // Mock default behavior for use cases and repository
        coEvery { mockLoadCustomizationUseCase.invoke() } returns customizationConfigFlow
        coEvery { mockCustomizationRepository.updateGlobalEnabled(any()) } returns Result.success(Unit)

        viewModel = CustomizeViewModel(
            loadCustomizationUseCase = mockLoadCustomizationUseCase,
            saveCustomizationUseCase = mockSaveCustomizationUseCase,
            emojiItemService = mockEmojiItemService,
            customizationRepository = mockCustomizationRepository,
            context = context
        )
    }

    @Test
    fun `toggleGlobalEnabled true with permissions should start service`() = runTest {
        // Arrange
        mockkObject(EmojiOverlayPermissionManager)
        every { EmojiOverlayPermissionManager.hasAllRequiredPermissions(context) } returns true

        // Spy on the context to verify startService calls
        val contextSpy = spyk(context)
        // Re-initialize ViewModel with the spied context
        viewModel = CustomizeViewModel(
            loadCustomizationUseCase = mockLoadCustomizationUseCase,
            saveCustomizationUseCase = mockSaveCustomizationUseCase,
            emojiItemService = mockEmojiItemService,
            customizationRepository = mockCustomizationRepository,
            context = contextSpy
        )

        // Act
        viewModel.handleEvent(CustomizeEvent.ToggleGlobalEnabled(true))
        advanceUntilIdle()

        // Assert
        verify { contextSpy.startService(match<Intent> { it.action == EmojiBatteryAccessibilityService.ACTION_SHOW_OVERLAY }) }
        coVerify { mockCustomizationRepository.updateGlobalEnabled(true) }
        assertEquals(true, viewModel.uiState.value.isGlobalEnabled)
    }

    @Test
    fun `toggleGlobalEnabled false should stop service`() = runTest {
        // Arrange
        // Simulate it's currently enabled
        viewModel.handleEvent(CustomizeEvent.ToggleGlobalEnabled(true))
        advanceUntilIdle()
        assertEquals(true, viewModel.uiState.value.isGlobalEnabled)

        // Spy on the context to verify startService calls
        val contextSpy = spyk(context)
        // Re-initialize ViewModel with the spied context
        viewModel = CustomizeViewModel(
            loadCustomizationUseCase = mockLoadCustomizationUseCase,
            saveCustomizationUseCase = mockSaveCustomizationUseCase,
            emojiItemService = mockEmojiItemService,
            customizationRepository = mockCustomizationRepository,
            context = contextSpy
        )

        // Act
        viewModel.handleEvent(CustomizeEvent.ToggleGlobalEnabled(false))
        advanceUntilIdle()

        // Assert
        verify { contextSpy.startService(match<Intent> { it.action == EmojiBatteryAccessibilityService.ACTION_HIDE_OVERLAY }) }
        coVerify { mockCustomizationRepository.updateGlobalEnabled(false) }
        assertEquals(false, viewModel.uiState.value.isGlobalEnabled)
    }

    @Test
    fun `handlePermissionResult granted should enable feature and start service`() = runTest {
        // Arrange
        // Simulate initial state where permissions were requested
        viewModel.handleEvent(CustomizeEvent.ToggleGlobalEnabled(true))
        advanceUntilIdle()
        assertEquals(true, viewModel.uiState.value.shouldRequestPermissions)

        // Spy on the context to verify startService calls
        val contextSpy = spyk(context)
        // Re-initialize ViewModel with the spied context
        viewModel = CustomizeViewModel(
            loadCustomizationUseCase = mockLoadCustomizationUseCase,
            saveCustomizationUseCase = mockSaveCustomizationUseCase,
            emojiItemService = mockEmojiItemService,
            customizationRepository = mockCustomizationRepository,
            context = contextSpy
        )

        // Act
        every { EmojiOverlayPermissionManager.hasAllRequiredPermissions(context) } returns true // Simulate permission granted
        viewModel.handleEvent(CustomizeEvent.HandlePermissionResult(true))
        advanceUntilIdle()

        // Assert
        verify { contextSpy.startService(match<Intent> { it.action == EmojiBatteryAccessibilityService.ACTION_SHOW_OVERLAY }) }
        coVerify { mockCustomizationRepository.updateGlobalEnabled(true) }
        assertEquals(true, viewModel.uiState.value.isGlobalEnabled)
        assertEquals(false, viewModel.uiState.value.shouldRequestPermissions)
    }

    @Test
    fun `handlePermissionResult denied should disable feature`() = runTest {
        // Arrange
        // Simulate initial state where permissions were requested
        viewModel.handleEvent(CustomizeEvent.ToggleGlobalEnabled(true))
        advanceUntilIdle()
        assertEquals(true, viewModel.uiState.value.shouldRequestPermissions)

        // Spy on the context to verify startService calls
        val contextSpy = spyk(context)
        // Re-initialize ViewModel with the spied context
        viewModel = CustomizeViewModel(
            loadCustomizationUseCase = mockLoadCustomizationUseCase,
            saveCustomizationUseCase = mockSaveCustomizationUseCase,
            emojiItemService = mockEmojiItemService,
            customizationRepository = mockCustomizationRepository,
            context = contextSpy
        )

        // Act
        viewModel.handleEvent(CustomizeEvent.HandlePermissionResult(false))
        advanceUntilIdle()

        // Assert
        verify(inverse = true) { contextSpy.startService(any()) } // Service should not be started
        coVerify(inverse = true) { mockCustomizationRepository.updateGlobalEnabled(true) } // Should not try to enable
        assertEquals(false, viewModel.uiState.value.isGlobalEnabled)
        assertEquals(false, viewModel.uiState.value.shouldRequestPermissions)
    }
}