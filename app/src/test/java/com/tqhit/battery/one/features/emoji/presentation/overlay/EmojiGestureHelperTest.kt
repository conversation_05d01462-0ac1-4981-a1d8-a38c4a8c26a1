package com.tqhit.battery.one.features.emoji.presentation.overlay

import android.view.MotionEvent
import com.tqhit.battery.one.utils.BatteryLogger
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [33])
class EmojiGestureHelperTest {

    private lateinit var gestureHelper: EmojiGestureHelper

    @Before
    fun setUp() {
        mockkStatic(BatteryLogger::class)
        gestureHelper = EmojiGestureHelper()
    }

    private fun createMotionEvent(x: Float, y: Float): MotionEvent {
        return MotionEvent.obtain(0, 0, MotionEvent.ACTION_DOWN, x, y, 0)
    }

    @Test
    fun `analyzeGesture should return SWIPE_DOWN_LEFT for left swipe`() {
        val e1 = createMotionEvent(100f, 10f)
        val e2 = createMotionEvent(100f, 200f)
        val result = gestureHelper.analyzeGesture(e1, e2, 0f, 2000f, 1080)
        assertEquals(EmojiGestureHelper.GestureType.SWIPE_DOWN_LEFT, result.gestureType)
    }

    @Test
    fun `analyzeGesture should return SWIPE_DOWN_RIGHT for right swipe`() {
        val e1 = createMotionEvent(900f, 10f)
        val e2 = createMotionEvent(900f, 200f)
        val result = gestureHelper.analyzeGesture(e1, e2, 0f, 2000f, 1080)
        assertEquals(EmojiGestureHelper.GestureType.SWIPE_DOWN_RIGHT, result.gestureType)
    }

    @Test
    fun `analyzeGesture should return SWIPE_DOWN_CENTER for center swipe`() {
        val e1 = createMotionEvent(500f, 10f)
        val e2 = createMotionEvent(500f, 200f)
        val result = gestureHelper.analyzeGesture(e1, e2, 0f, 2000f, 1080)
        assertEquals(EmojiGestureHelper.GestureType.SWIPE_DOWN_CENTER, result.gestureType)
    }

    @Test
    fun `analyzeGesture should return NONE for horizontal swipe`() {
        val e1 = createMotionEvent(100f, 100f)
        val e2 = createMotionEvent(500f, 100f)
        val result = gestureHelper.analyzeGesture(e1, e2, 2000f, 0f, 1080)
        assertEquals(EmojiGestureHelper.GestureType.NONE, result.gestureType)
    }

    @Test
    fun `analyzeGesture should return NONE for swipe that is not long enough`() {
        val e1 = createMotionEvent(500f, 10f)
        val e2 = createMotionEvent(500f, 50f)
        val result = gestureHelper.analyzeGesture(e1, e2, 0f, 2000f, 1080)
        assertEquals(EmojiGestureHelper.GestureType.NONE, result.gestureType)
    }
}