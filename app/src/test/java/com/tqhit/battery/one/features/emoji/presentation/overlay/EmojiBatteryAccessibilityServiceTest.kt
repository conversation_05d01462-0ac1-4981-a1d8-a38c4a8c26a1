package com.tqhit.battery.one.features.emoji.presentation.overlay

import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.view.WindowManager
import androidx.test.core.app.ApplicationProvider
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase
import dagger.hilt.android.testing.BindValue
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import dagger.hilt.android.testing.HiltTestApplication
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import kotlinx.coroutines.flow.flowOf
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@HiltAndroidTest
@RunWith(RobolectricTestRunner::class)
@Config(application = HiltTestApplication::class, sdk = [33])
class EmojiBatteryAccessibilityServiceTest {

    @get:Rule
    var hiltRule = HiltAndroidRule(this)

    @BindValue
    @JvmField
    val loadCustomizationUseCase: LoadCustomizationUseCase = mockk()

    private lateinit var service: EmojiBatteryAccessibilityService
    private lateinit var windowManager: WindowManager

    @Before
    fun setUp() {
        hiltRule.inject()
        windowManager = mockk(relaxed = true)

        coEvery { loadCustomizationUseCase.getCurrentConfig() } returns CustomizationConfig.createDefault().copy(isGlobalEnabled = true)
        every { loadCustomizationUseCase.invoke() } returns flowOf(CustomizationConfig.createDefault().copy(isGlobalEnabled = true))

        service = spyk(EmojiBatteryAccessibilityService(), recordPrivateCalls = true)
        every { service.getSystemService(Context.WINDOW_SERVICE) } returns windowManager
        service.onCreate()
    }

    @Test
    fun onServiceConnected_whenEnabled_shouldAddView() {
        service.onServiceConnected()
        verify { windowManager.addView(any(), any()) }
    }

    @Test
    fun onNotificationActionRequested_withLeftSwipe_shouldPerformNotificationsAction() {
        every { service.performGlobalAction(any()) } returns true
        service.onNotificationActionRequested(EmojiGestureHelper.GestureType.SWIPE_DOWN_LEFT)
        verify { service.performGlobalAction(AccessibilityService.GLOBAL_ACTION_NOTIFICATIONS) }
    }

    @Test
    fun onNotificationActionRequested_withRightSwipe_shouldPerformQuickSettingsAction() {
        every { service.performGlobalAction(any()) } returns true
        service.onNotificationActionRequested(EmojiGestureHelper.GestureType.SWIPE_DOWN_RIGHT)
        verify { service.performGlobalAction(AccessibilityService.GLOBAL_ACTION_QUICK_SETTINGS) }
    }

    @Test
    fun onDestroy_shouldRemoveView() {
        service.onServiceConnected()
        verify { windowManager.addView(any(), any()) }
        service.onDestroy()
        verify { windowManager.removeView(any()) }
    }
}