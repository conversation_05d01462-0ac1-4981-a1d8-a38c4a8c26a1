package com.tqhit.battery.one.features.emoji.presentation.overlay

import android.app.Application
import android.content.Context
import android.graphics.Color
import androidx.test.core.app.ApplicationProvider
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import org.junit.Assert.assertNotNull
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [33], application = Application::class)
class EmojiBatteryViewTest {

    private lateinit var context: Context
    private lateinit var emojiBatteryView: EmojiBatteryView

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        context.setTheme(android.R.style.Theme_DeviceDefault)
        emojiBatteryView = EmojiBatteryView(context)
    }

    @Test
    fun `view initializes without crashing`() {
        assertNotNull(emojiBatteryView)
    }

    @Test
    fun `updateCustomizationConfig should not crash view`() {
        val config = CustomizationConfig.createDefault().copy(
            customConfig = CustomizationConfig.createDefault().customConfig.copy(
                percentageColor = Color.RED,
                percentageFontSizeDp = 20
            )
        )
        emojiBatteryView.updateCustomizationConfig(config)
        assertNotNull(emojiBatteryView)
    }
}